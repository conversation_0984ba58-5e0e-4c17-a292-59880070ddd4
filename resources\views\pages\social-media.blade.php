<x-app-layout title="Application List" is-header-blur="true">

    <main class="main-content w-full px-[var(--margin-x)] pb-8">
        <div class="mt-12 text-center">
            <div class="avatar size-16">
                <div class="is-initial rounded-full bg-linear-to-br from-pink-500 to-rose-500 text-white">
                    <i class="fa-solid fa-shapes text-2xl"></i>
                </div>
            </div>
            <h3 class="mt-3 text-xl font-semibold text-slate-600 dark:text-navy-100">
                Social Media Bots
            </h3>
            <p class="mt-0.5 text-base">
                Our Dadecated Social Bots
            </p>
        </div>
        <div
            class="mx-auto mt-8 grid w-full max-w-6xl grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 sm:gap-5 lg:gap-6">
            <!-- WhatsApp Card -->
            <div class="card p-4 sm:p-5 shadow-lg hover:shadow-xl transition-shadow duration-300 ease-in-out">
                <div class="avatar size-12">
                    <div class="is-initial rounded-full bg-green-500 text-white">
                        <img class="size-6" src="{{ asset('images/logos/whatsapp-round.svg') }}" alt="WhatsApp">
                    </div>
                </div>
                <h2 class="mt-5 text-base font-medium tracking-wide text-slate-700 line-clamp-1 dark:text-navy-100">
                    WhatsApp Bot
                </h2>
                <p class="mt-1">
                    Automate customer support and communication on WhatsApp.
                </p>
                <div class="mt-5 pb-1">
                    <a wire:navigate href="{{ route('social/whatsapp') }}"
                        class="border-b border-dashed border-current pb-0.5 font-medium text-primary outline-hidden transition-colors duration-300 hover:text-primary/70 focus:text-primary/70 dark:text-accent-light dark:hover:text-accent-light/70 dark:focus:text-accent-light/70">View
                        Application</a>
                </div>
            </div>

            <!-- Telegram Card -->
            <div class="card p-4 sm:p-5 shadow-lg hover:shadow-xl transition-shadow duration-300 ease-in-out">
                <div class="avatar size-12">
                    <div class="is-initial rounded-full bg-blue-400 text-white">
                        <img class="size-6" src="{{ asset('images/logos/telegram-round.svg') }}" alt="Telegram">
                    </div>
                </div>
                <h2 class="mt-5 text-base font-medium tracking-wide text-slate-700 line-clamp-1 dark:text-navy-100">
                    Telegram Bot
                </h2>
                <p class="mt-1">
                    Automate messaging and group management on Telegram.
                </p>
                <div class="mt-5 pb-1">
                    <a wire:navigate href="{{ route('social/telegram') }}"
                        class="border-b border-dashed border-current pb-0.5 font-medium text-primary outline-hidden transition-colors duration-300 hover:text-primary/70 focus:text-primary/70 dark:text-accent-light dark:hover:text-accent-light/70 dark:focus:text-accent-light/70">View
                        Application</a>
                </div>
            </div>

            <!-- Instagram Card -->
            <div class="card p-4 sm:p-5 shadow-lg hover:shadow-xl transition-shadow duration-300 ease-in-out">
                <div class="avatar size-12">
                    <div class="is-initial rounded-full bg-pink-600 text-white">
                        <img class="size-6" src="{{ asset('images/logos/instagram-round.svg') }}" alt="Instagram">
                    </div>
                </div>
                <h2 class="mt-5 text-base font-medium tracking-wide text-slate-700 line-clamp-1 dark:text-navy-100">
                    Instagram Bot
                </h2>
                <p class="mt-1">
                    Automate your Instagram activities and grow your audience.
                </p>
                <div class="mt-5 pb-1">
                    <a wire:navigate href="{{ route('social/instagram') }}"
                        class="border-b border-dashed border-current pb-0.5 font-medium text-primary outline-hidden transition-colors duration-300 hover:text-primary/70 focus:text-primary/70 dark:text-accent-light dark:hover:text-accent-light/70 dark:focus:text-accent-light/70">View
                        Application</a>
                </div>
            </div>

            <!-- Discord Card -->
            <div class="card p-4 sm:p-5 shadow-lg hover:shadow-xl transition-shadow duration-300 ease-in-out">
                <div class="avatar size-12">
                    <div class="is-initial rounded-full bg-indigo-600 text-white">
                        <img class="size-6" src="{{ asset('images/logos/discord-round.svg') }}" alt="Discord">
                    </div>
                </div>
                <h2 class="mt-5 text-base font-medium tracking-wide text-slate-700 line-clamp-1 dark:text-navy-100">
                    Discord Bot
                </h2>
                <p class="mt-1">
                    Manage your Discord server and automate tasks.
                </p>
                <div class="mt-5 pb-1">
                    <a wire:navigate href="{{ route('social/discord') }}"
                        class="border-b border-dashed border-current pb-0.5 font-medium text-primary outline-hidden transition-colors duration-300 hover:text-primary/70 focus:text-primary/70 dark:text-accent-light dark:hover:text-accent-light/70 dark:focus:text-accent-light/70">View
                        Application</a>
                </div>
            </div>

            <!-- Facebook Card -->
            <div class="card p-4 sm:p-5 shadow-lg hover:shadow-xl transition-shadow duration-300 ease-in-out">
                <div class="avatar size-12">
                    <div class="is-initial rounded-full bg-blue-600 text-white">
                        <img class="size-6" src="{{ asset('images/logos/facebook-round.svg') }}" alt="Facebook">
                    </div>
                </div>
                <h2 class="mt-5 text-base font-medium tracking-wide text-slate-700 line-clamp-1 dark:text-navy-100">
                    Facebook Bot
                </h2>
                <p class="mt-1">
                    Automate your Facebook interactions with our dedicated bot.
                </p>
                <div class="mt-5 pb-1">
                    <a wire:navigate href="{{ route('social/facebook') }}"
                        class="border-b border-dashed border-current pb-0.5 font-medium text-primary outline-hidden transition-colors duration-300 hover:text-primary/70 focus:text-primary/70 dark:text-accent-light dark:hover:text-accent-light/70 dark:focus:text-accent-light/70">View
                        Application</a>
                </div>
            </div>
        </div>
    </main>
</x-app-layout>
