<div class="card">
    <div class="flex flex-col items-center space-y-4 border-b border-slate-200 p-4 dark:border-navy-500 sm:flex-row sm:justify-between sm:space-y-0 sm:px-5">
        <h2 class="text-lg font-medium tracking-wide text-slate-700 dark:text-navy-100">
            Privacy & Data Management
        </h2>
        <div class="flex justify-center space-x-2">
            <button wire:click="cancel"
                class="btn min-w-[7rem] rounded-full border border-slate-300 font-medium text-slate-700 hover:bg-slate-150 focus:bg-slate-150 active:bg-slate-150/80 dark:border-navy-450 dark:text-navy-100 dark:hover:bg-navy-500 dark:focus:bg-navy-500 dark:active:bg-navy-500/90">
                Cancel
            </button>
            <button wire:click="save"
                class="btn min-w-[7rem] rounded-full bg-primary font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90 dark:bg-accent dark:hover:bg-accent-focus dark:focus:bg-accent-focus dark:active:bg-accent/90">
                Save
            </button>
        </div>
    </div>

    <div class="p-4 sm:p-5">
        <!-- Flash Messages -->
        <!--[if BLOCK]><![endif]--><?php if(session()->has('message')): ?>
            <div class="alert mb-4 flex rounded-lg border border-success bg-success/10 py-4 px-4 text-success sm:px-5">
                <svg xmlns="http://www.w3.org/2000/svg" class="size-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                <div class="ml-2">
                    <h3 class="text-sm font-medium"><?php echo e(session('message')); ?></h3>
                </div>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        <?php if(session()->has('warning')): ?>
            <div class="alert mb-4 flex rounded-lg border border-warning bg-warning/10 py-4 px-4 text-warning sm:px-5">
                <svg xmlns="http://www.w3.org/2000/svg" class="size-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
                <div class="ml-2">
                    <h3 class="text-sm font-medium"><?php echo e(session('warning')); ?></h3>
                </div>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        <div class="space-y-6">
            <!-- Profile Visibility -->
            <div>
                <h3 class="text-base font-medium text-slate-600 dark:text-navy-100 mb-4">
                    Profile Visibility
                </h3>
                
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
                    <!-- Profile Visibility -->
                    <label class="block">
                        <span>Profile Visibility</span>
                        <select wire:model="profileVisibility"
                            class="form-select mt-1.5 w-full rounded-lg border border-slate-300 bg-white px-3 py-2 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:bg-navy-700 dark:hover:border-navy-400 dark:focus:border-accent">
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $visibilityOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($key); ?>"><?php echo e($label); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </select>
                    </label>

                    <!-- Email Visibility -->
                    <label class="block">
                        <span>Email Visibility</span>
                        <select wire:model="emailVisibility"
                            class="form-select mt-1.5 w-full rounded-lg border border-slate-300 bg-white px-3 py-2 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:bg-navy-700 dark:hover:border-navy-400 dark:focus:border-accent">
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $visibilityOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($key); ?>"><?php echo e($label); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </select>
                    </label>

                    <!-- Phone Visibility -->
                    <label class="block">
                        <span>Phone Visibility</span>
                        <select wire:model="phoneVisibility"
                            class="form-select mt-1.5 w-full rounded-lg border border-slate-300 bg-white px-3 py-2 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:bg-navy-700 dark:hover:border-navy-400 dark:focus:border-accent">
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $visibilityOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($key); ?>"><?php echo e($label); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </select>
                    </label>
                </div>
            </div>

            <div class="my-7 h-px bg-slate-200 dark:bg-navy-500"></div>

            <!-- Data & Analytics -->
            <div>
                <h3 class="text-base font-medium text-slate-600 dark:text-navy-100 mb-4">
                    Data & Analytics
                </h3>
                
                <div class="space-y-4">
                    <label class="inline-flex items-center space-x-2">
                        <input wire:model="activityTracking"
                            class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                            type="checkbox" />
                        <span>Enable activity tracking</span>
                    </label>

                    <label class="inline-flex items-center space-x-2">
                        <input wire:model="analyticsEnabled"
                            class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                            type="checkbox" />
                        <span>Enable analytics collection</span>
                    </label>

                    <!-- Data Retention -->
                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                        <label class="block">
                            <span>Data Retention Period</span>
                            <select wire:model="dataRetentionPeriod"
                                class="form-select mt-1.5 w-full rounded-lg border border-slate-300 bg-white px-3 py-2 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:bg-navy-700 dark:hover:border-navy-400 dark:focus:border-accent">
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $retentionOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($key); ?>"><?php echo e($label); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </select>
                        </label>
                    </div>
                </div>
            </div>

            <div class="my-7 h-px bg-slate-200 dark:bg-navy-500"></div>

            <!-- Communication Preferences -->
            <div>
                <h3 class="text-base font-medium text-slate-600 dark:text-navy-100 mb-4">
                    Communication Preferences
                </h3>
                
                <div class="space-y-4">
                    <label class="inline-flex items-center space-x-2">
                        <input wire:model="marketingEmails"
                            class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                            type="checkbox" />
                        <span>Receive marketing emails</span>
                    </label>

                    <label class="inline-flex items-center space-x-2">
                        <input wire:model="notificationEmails"
                            class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                            type="checkbox" />
                        <span>Receive notification emails</span>
                    </label>

                    <label class="inline-flex items-center space-x-2">
                        <input wire:model="loginNotifications"
                            class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                            type="checkbox" />
                        <span>Receive login notifications</span>
                    </label>
                </div>
            </div>

            <div class="my-7 h-px bg-slate-200 dark:bg-navy-500"></div>

            <!-- Security Settings -->
            <div>
                <h3 class="text-base font-medium text-slate-600 dark:text-navy-100 mb-4">
                    Security Settings
                </h3>
                
                <div class="space-y-4">
                    <!-- Two-Factor Authentication -->
                    <div class="flex items-center justify-between p-4 border border-slate-200 rounded-lg dark:border-navy-500">
                        <div>
                            <h4 class="font-medium">Two-Factor Authentication</h4>
                            <p class="text-sm text-slate-400 dark:text-navy-300">
                                Add an extra layer of security to your account
                            </p>
                        </div>
                        <button wire:click="enableTwoFactor"
                            class="btn <?php echo e($twoFactorEnabled ? 'bg-success text-white' : 'border border-slate-300 text-slate-700 dark:border-navy-450 dark:text-navy-100'); ?> rounded-full px-4 py-2">
                            <?php echo e($twoFactorEnabled ? 'Enabled' : 'Enable'); ?>

                        </button>
                    </div>

                    <!-- Session Timeout -->
                    <label class="block">
                        <span>Session Timeout (minutes)</span>
                        <input wire:model="sessionTimeout"
                            class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                            type="number" min="5" max="1440" />
                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['sessionTimeout'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-xs text-error"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                    </label>
                </div>
            </div>

            <div class="my-7 h-px bg-slate-200 dark:bg-navy-500"></div>

            <!-- Data Management Actions -->
            <div>
                <h3 class="text-base font-medium text-slate-600 dark:text-navy-100 mb-4">
                    Data Management
                </h3>
                
                <div class="space-y-4">
                    <!-- Download Data -->
                    <div class="flex items-center justify-between p-4 border border-slate-200 rounded-lg dark:border-navy-500">
                        <div>
                            <h4 class="font-medium">Download Your Data</h4>
                            <p class="text-sm text-slate-400 dark:text-navy-300">
                                Export all your personal data in a portable format
                            </p>
                        </div>
                        <button wire:click="downloadData"
                            class="btn border border-slate-300 text-slate-700 hover:bg-slate-150 focus:bg-slate-150 active:bg-slate-150/80 dark:border-navy-450 dark:text-navy-100 dark:hover:bg-navy-500 dark:focus:bg-navy-500 dark:active:bg-navy-500/90 rounded-full px-4 py-2">
                            Download
                        </button>
                    </div>

                    <!-- Clear Activity Log -->
                    <div class="flex items-center justify-between p-4 border border-slate-200 rounded-lg dark:border-navy-500">
                        <div>
                            <h4 class="font-medium">Clear Activity Log</h4>
                            <p class="text-sm text-slate-400 dark:text-navy-300">
                                Remove all recorded activity from your account
                            </p>
                        </div>
                        <button wire:click="clearActivityLog"
                            class="btn border border-warning text-warning hover:bg-warning/10 focus:bg-warning/10 active:bg-warning/20 rounded-full px-4 py-2">
                            Clear
                        </button>
                    </div>

                    <!-- Revoke All Sessions -->
                    <div class="flex items-center justify-between p-4 border border-slate-200 rounded-lg dark:border-navy-500">
                        <div>
                            <h4 class="font-medium">Revoke All Sessions</h4>
                            <p class="text-sm text-slate-400 dark:text-navy-300">
                                Sign out from all devices except this one
                            </p>
                        </div>
                        <button wire:click="revokeAllSessions"
                            class="btn border border-warning text-warning hover:bg-warning/10 focus:bg-warning/10 active:bg-warning/20 rounded-full px-4 py-2">
                            Revoke
                        </button>
                    </div>

                    <!-- Delete Account -->
                    <div class="flex items-center justify-between p-4 border border-error rounded-lg bg-error/5">
                        <div>
                            <h4 class="font-medium text-error">Delete Account</h4>
                            <p class="text-sm text-slate-400 dark:text-navy-300">
                                Permanently delete your account and all associated data
                            </p>
                        </div>
                        <button wire:click="deleteAccount"
                            class="btn bg-error text-white hover:bg-error-focus focus:bg-error-focus active:bg-error-focus/90 rounded-full px-4 py-2">
                            Delete
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH D:\Laravel_Projects\alhars\resources\views/livewire/settings/privacy-data-tab.blade.php ENDPATH**/ ?>