<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::middleware('guest')->group(function () {
    Route::get('/login', [\App\Http\Controllers\AuthController::class, 'loginView'])->name('loginView');
    Route::post('/login', [\App\Http\Controllers\AuthController::class, 'login'])->name('login');
    Route::get('/register', [\App\Http\Controllers\AuthController::class, 'registerView'])->name('registerView');
    Route::post('/register', [\App\Http\Controllers\AuthController::class, 'register'])->name('register');
});

Route::middleware('auth')->group(function () {
    Route::post('/logout', [\App\Http\Controllers\AuthController::class, 'logout'])->name('logout');
    Route::get('/', \App\Livewire\Dashboards\CrmAnalytics::class)->name('index');

    Route::view('/settings', 'pages.settings')->name('settings');

    Route::get('/elements/avatar', \App\Livewire\Elements\Avatar::class)->name('elements/avatar');
    Route::get('/elements/alert', \App\Livewire\Elements\Alert::class)->name('elements/alert');
    Route::get('/elements/button', \App\Livewire\Elements\Button::class)->name('elements/button');
    Route::get('/elements/button-group', \App\Livewire\Elements\ButtonGroup::class)->name('elements/button-group');
    Route::get('/elements/badge', \App\Livewire\Elements\Badge::class)->name('elements/badge');
    Route::get('/elements/breadcrumb', \App\Livewire\Elements\Breadcrumb::class)->name('elements/breadcrumb');
    Route::get('/elements/card', \App\Livewire\Elements\Card::class)->name('elements/card');
    Route::get('/elements/divider', \App\Livewire\Elements\Divider::class)->name('elements/divider');
    Route::get('/elements/mask', \App\Livewire\Elements\Mask::class)->name('elements/mask');
    Route::get('/elements/progress', \App\Livewire\Elements\Progress::class)->name('elements/progress');
    Route::get('/elements/skeleton', \App\Livewire\Elements\Skeleton::class)->name('elements/skeleton');
    Route::get('/elements/spinner', \App\Livewire\Elements\Spinner::class)->name('elements/spinner');
    Route::get('/elements/tag', \App\Livewire\Elements\Tag::class)->name('elements/tag');
    Route::get('/elements/tooltip', \App\Livewire\Elements\Tooltip::class)->name('elements/tooltip');
    Route::get('/elements/typography', \App\Livewire\Elements\Typography::class)->name('elements/typography');

    Route::get('/components/accordion', \App\Livewire\Components\Accordion::class)->name('components/accordion');
    Route::get('/components/collapse', \App\Livewire\Components\Collapse::class)->name('components/collapse');
    Route::get('/components/tab', \App\Livewire\Components\Tab::class)->name('components/tab');
    Route::get('/components/dropdown', \App\Livewire\Components\Dropdown::class)->name('components/dropdown');
    Route::get('/components/popover', \App\Livewire\Components\Popover::class)->name('components/popover');
    Route::get('/components/modal', \App\Livewire\Components\Modal::class)->name('components/modal');
    Route::get('/components/drawer', \App\Livewire\Components\Drawer::class)->name('components/drawer');
    Route::get('/components/steps', \App\Livewire\Components\Steps::class)->name('components/steps');
    Route::get('/components/timeline', \App\Livewire\Components\Timeline::class)->name('components/timeline');
    Route::get('/components/pagination', \App\Livewire\Components\Pagination::class)->name('components/pagination');
    Route::get('/components/menu-list', \App\Livewire\Components\MenuList::class)->name('components/menu-list');
    Route::get('/components/treeview', \App\Livewire\Components\Treeview::class)->name('components/treeview');
    Route::get('/components/table', \App\Livewire\Components\Table::class)->name('components/table');
    Route::get('/components/table-advanced', \App\Livewire\Components\TableAdvanced::class)->name('components/table-advanced');
    Route::get('/components/table-gridjs', \App\Livewire\Components\TableGridjs::class)->name('components/gridjs');
    Route::get('/components/apexchart', \App\Livewire\Components\Apexchart::class)->name('components/apexchart');
    Route::get('/components/carousel', \App\Livewire\Components\Carousel::class)->name('components/carousel');
    Route::get('/components/notification', \App\Livewire\Components\Notification::class)->name('components/notification');
    Route::get('/components/extension-clipboard', \App\Livewire\Components\ExtensionClipboard::class)->name('components/extension-clipboard');
    Route::get('/components/extension-persist', \App\Livewire\Components\ExtensionPersist::class)->name('components/extension-persist');
    Route::get('/components/extension-monochrome', \App\Livewire\Components\ExtensionMonochrome::class)->name('components/extension-monochrome');

    Route::get('/forms/layout-v1', \App\Livewire\Forms\LayoutV1::class)->name('forms/layout-v1');
    Route::get('/forms/layout-v2', \App\Livewire\Forms\LayoutV2::class)->name('forms/layout-v2');
    Route::get('/forms/layout-v3', \App\Livewire\Forms\LayoutV3::class)->name('forms/layout-v3');
    Route::get('/forms/layout-v4', \App\Livewire\Forms\LayoutV4::class)->name('forms/layout-v4');
    Route::get('/forms/layout-v5', \App\Livewire\Forms\LayoutV5::class)->name('forms/layout-v5');
    Route::get('/forms/input-text', \App\Livewire\Forms\InputText::class)->name('forms/input-text');
    Route::get('/forms/input-group', \App\Livewire\Forms\InputGroup::class)->name('forms/input-group');
    Route::get('/forms/input-mask', \App\Livewire\Forms\InputMask::class)->name('forms/input-mask');
    Route::get('/forms/checkbox', \App\Livewire\Forms\Checkbox::class)->name('forms/checkbox');
    Route::get('/forms/radio', \App\Livewire\Forms\Radio::class)->name('forms/radio');
    Route::get('/forms/switch', \App\Livewire\Forms\SwitchForm::class)->name('forms/switch');
    Route::get('/forms/select', \App\Livewire\Forms\Select::class)->name('forms/select');
    Route::get('/forms/tom-select', \App\Livewire\Forms\TomSelect::class)->name('forms/tom-select');
    Route::get('/forms/textarea', \App\Livewire\Forms\Textarea::class)->name('forms/textarea');
    Route::get('/forms/range', \App\Livewire\Forms\Range::class)->name('forms/range');
    Route::get('/forms/datepicker', \App\Livewire\Forms\Datepicker::class)->name('forms/datepicker');
    Route::get('/forms/timepicker', \App\Livewire\Forms\Timepicker::class)->name('forms/timepicker');
    Route::get('/forms/datetimepicker', \App\Livewire\Forms\Datetimepicker::class)->name('forms/datetimepicker');
    Route::get('/forms/text-editor', \App\Livewire\Forms\TextEditor::class)->name('forms/text-editor');
    Route::get('/forms/upload', \App\Livewire\Forms\Upload::class)->name('forms/upload');
    Route::get('/forms/validation', \App\Livewire\Forms\Validation::class)->name('forms/validation');

    Route::get('/layouts/onboarding-1', \App\Livewire\Layouts\Onboarding1::class)->name('layouts/onboarding-1');
    Route::get('/layouts/onboarding-2', \App\Livewire\Layouts\Onboarding2::class)->name('layouts/onboarding-2');
    Route::get('/layouts/user-card-1', \App\Livewire\Layouts\UserCard1::class)->name('layouts/user-card-1');
    Route::get('/layouts/user-card-2', \App\Livewire\Layouts\UserCard2::class)->name('layouts/user-card-2');
    Route::get('/layouts/user-card-3', \App\Livewire\Layouts\UserCard3::class)->name('layouts/user-card-3');
    Route::get('/layouts/user-card-4', \App\Livewire\Layouts\UserCard4::class)->name('layouts/user-card-4');
    Route::get('/layouts/user-card-5', \App\Livewire\Layouts\UserCard5::class)->name('layouts/user-card-5');
    Route::get('/layouts/user-card-6', \App\Livewire\Layouts\UserCard6::class)->name('layouts/user-card-6');
    Route::get('/layouts/user-card-7', \App\Livewire\Layouts\UserCard7::class)->name('layouts/user-card-7');
    Route::get('/layouts/blog-card-1', \App\Livewire\Layouts\BlogCard1::class)->name('layouts/blog-card-1');
    Route::get('/layouts/blog-card-2', \App\Livewire\Layouts\BlogCard2::class)->name('layouts/blog-card-2');
    Route::get('/layouts/blog-card-3', \App\Livewire\Layouts\BlogCard3::class)->name('layouts/blog-card-3');
    Route::get('/layouts/blog-card-4', \App\Livewire\Layouts\BlogCard4::class)->name('layouts/blog-card-4');
    Route::get('/layouts/blog-card-5', \App\Livewire\Layouts\BlogCard5::class)->name('layouts/blog-card-5');
    Route::get('/layouts/blog-card-6', \App\Livewire\Layouts\BlogCard6::class)->name('layouts/blog-card-6');
    Route::get('/layouts/blog-card-7', \App\Livewire\Layouts\BlogCard7::class)->name('layouts/blog-card-7');
    Route::get('/layouts/blog-card-8', \App\Livewire\Layouts\BlogCard8::class)->name('layouts/blog-card-8');
    Route::get('/layouts/blog-details', \App\Livewire\Layouts\BlogDetails::class)->name('layouts/blog-details');
    Route::get('/layouts/help-1', \App\Livewire\Layouts\Help1::class)->name('layouts/help-1');
    Route::get('/layouts/help-2', \App\Livewire\Layouts\Help2::class)->name('layouts/help-2');
    Route::get('/layouts/help-3', \App\Livewire\Layouts\Help3::class)->name('layouts/help-3');
    Route::get('/layouts/price-list-1', \App\Livewire\Layouts\PriceList1::class)->name('layouts/price-list-1');
    Route::get('/layouts/price-list-2', \App\Livewire\Layouts\PriceList2::class)->name('layouts/price-list-2');
    Route::get('/layouts/price-list-3', \App\Livewire\Layouts\PriceList3::class)->name('layouts/price-list-3');
    Route::get('/layouts/price-list-4', \App\Livewire\Layouts\PriceList4::class)->name('layouts/price-list-4');
    Route::get('/layouts/invoice-1', \App\Livewire\Layouts\Invoice1::class)->name('layouts/invoice-1');
    Route::get('/layouts/invoice-2', \App\Livewire\Layouts\Invoice2::class)->name('layouts/invoice-2');
    Route::get('/layouts/sign-in-1', \App\Livewire\Layouts\SignIn1::class)->name('layouts/sign-in-1');
    Route::get('/layouts/sign-in-2', \App\Livewire\Layouts\SignIn2::class)->name('layouts/sign-in-2');
    Route::get('/layouts/sign-up-1', \App\Livewire\Layouts\SignUp1::class)->name('layouts/sign-up-1');
    Route::get('/layouts/sign-up-2', \App\Livewire\Layouts\SignUp2::class)->name('layouts/sign-up-2');
    Route::get('/layouts/error-404-1', \App\Livewire\Layouts\Error4041::class)->name('layouts/error-404-1');
    Route::get('/layouts/error-404-2', \App\Livewire\Layouts\Error4042::class)->name('layouts/error-404-2');
    Route::get('/layouts/error-404-3', \App\Livewire\Layouts\Error4043::class)->name('layouts/error-404-3');
    Route::get('/layouts/error-404-4', \App\Livewire\Layouts\Error4044::class)->name('layouts/error-404-4');
    Route::get('/layouts/error-401', \App\Livewire\Layouts\Error401::class)->name('layouts/error-401');
    Route::get('/layouts/error-429', \App\Livewire\Layouts\Error429::class)->name('layouts/error-429');
    Route::get('/layouts/error-500', \App\Livewire\Layouts\Error500::class)->name('layouts/error-500');
    Route::get('/layouts/starter-blurred-header', \App\Livewire\Layouts\StarterBlurredHeader::class)->name('layouts/starter-blurred-header');
    Route::get('/layouts/starter-unblurred-header', \App\Livewire\Layouts\StarterUnblurredHeader::class)->name('layouts/starter-unblurred-header');
    Route::get('/layouts/starter-centered-link', \App\Livewire\Layouts\StarterCenteredLink::class)->name('layouts/starter-centered-link');
    Route::get('/layouts/starter-minimal-sidebar', \App\Livewire\Layouts\StarterMinimalSidebar::class)->name('layouts/starter-minimal-sidebar');
    Route::get('/layouts/starter-sideblock', \App\Livewire\Layouts\StarterSideblock::class)->name('layouts/starter-sideblock');

    Route::get('/apps/chat', \App\Livewire\Apps\Chat::class)->name('apps/chat');
    Route::get('/apps/ai-chat', \App\Livewire\Apps\AiChat::class)->name('apps/ai-chat');
    Route::get('/apps/filemanager', \App\Livewire\Apps\Filemanager::class)->name('apps/filemanager');
    Route::get('/apps/kanban', \App\Livewire\Apps\Kanban::class)->name('apps/kanban');
    Route::get('/apps/list', \App\Livewire\Apps\ListApp::class)->name('apps/list');
    Route::get('/apps/mail', \App\Livewire\Apps\Mail::class)->name('apps/mail');
    Route::get('/apps/nft-1', \App\Livewire\Apps\Nft1::class)->name('apps/nft1');
    Route::get('/apps/nft-2', \App\Livewire\Apps\Nft2::class)->name('apps/nft2');
    Route::get('/apps/pos', \App\Livewire\Apps\Pos::class)->name('apps/pos');
    Route::get('/apps/todo', \App\Livewire\Apps\Todo::class)->name('apps/todo');
    Route::get('/apps/jobs-board', \App\Livewire\Apps\JobsBoard::class)->name('apps/jobs-board');
    Route::get('/apps/travel', \App\Livewire\Apps\Travel::class)->name('apps/travel');

    Route::view('/social-media-bots', 'pages.social-media')->name('social/social-media');
    Route::get('/social-media-bots/whatsapp', \App\Livewire\SocialBots\Whatsapp::class)->name('social/whatsapp');
    Route::get('/social-media-bots/telegram', \App\Livewire\SocialBots\Telegram::class)->name('social/telegram');
    Route::get('/social-media-bots/instagram', \App\Livewire\SocialBots\Instagram::class)->name('social/instagram');
    Route::get('/social-media-bots/discord', \App\Livewire\SocialBots\Discord::class)->name('social/discord');
    Route::get('/social-media-bots/facebook', \App\Livewire\SocialBots\Facebook::class)->name('social/facebook');



    Route::get('/dashboards/crm-analytics', \App\Livewire\Dashboards\CrmAnalytics::class)->name('dashboards/crm-analytics');
    Route::get('/dashboards/orders', \App\Livewire\Dashboards\Orders::class)->name('dashboards/orders');
    Route::get('/dashboards/crypto-1', \App\Livewire\Dashboards\Crypto1::class)->name('dashboards/crypto-1');
    Route::get('/dashboards/crypto-2', \App\Livewire\Dashboards\Crypto2::class)->name('dashboards/crypto-2');
    Route::get('/dashboards/banking-1', \App\Livewire\Dashboards\Banking1::class)->name('dashboards/banking-1');
    Route::get('/dashboards/banking-2', \App\Livewire\Dashboards\Banking2::class)->name('dashboards/banking-2');
    Route::get('/dashboards/personal', \App\Livewire\Dashboards\Personal::class)->name('dashboards/personal');
    Route::get('/dashboards/cms-analytics', \App\Livewire\Dashboards\CmsAnalytics::class)->name('dashboards/cms-analytics');
    Route::get('/dashboards/influencer', \App\Livewire\Dashboards\Influencer::class)->name('dashboards/influencer');
    Route::get('/dashboards/travel', \App\Livewire\Dashboards\TravelDashboard::class)->name('dashboards/travel');
    Route::get('/dashboards/teacher', \App\Livewire\Dashboards\Teacher::class)->name('dashboards/teacher');
    Route::get('/dashboards/education', \App\Livewire\Dashboards\Education::class)->name('dashboards/education');
    Route::get('/dashboards/authors', \App\Livewire\Dashboards\Authors: <AUTHORS>
    Route::get('/dashboards/doctor', \App\Livewire\Dashboards\Doctor::class)->name('dashboards/doctor');
    Route::get('/dashboards/employees', \App\Livewire\Dashboards\Employees::class)->name('dashboards/employees');
    Route::get('/dashboards/workspaces', \App\Livewire\Dashboards\Workspaces::class)->name('dashboards/workspaces');
    Route::get('/dashboards/meetings', \App\Livewire\Dashboards\Meetings::class)->name('dashboards/meetings');
    Route::get('/dashboards/project-boards', \App\Livewire\Dashboards\ProjectBoards::class)->name('dashboards/project-boards');
    Route::get('/dashboards/widget-ui', \App\Livewire\Dashboards\WidgetUi::class)->name('dashboards/widget-ui');
    Route::get('/dashboards/widget-contacts', \App\Livewire\Dashboards\WidgetContacts::class)->name('dashboards/widget-contacts');
});
