<div class="space-y-6">
    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="alert flex rounded-lg border border-success bg-success/10 py-4 px-4 text-success sm:px-5">
            <svg xmlns="http://www.w3.org/2000/svg" class="size-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            <div class="ml-2">
                <h3 class="text-sm font-medium">{{ session('message') }}</h3>
            </div>
        </div>
    @endif

    <!-- Connection Status -->
    <div class="flex items-center justify-between p-4 border border-slate-200 rounded-lg dark:border-navy-500 {{ $isConnected ? 'bg-success/5 border-success/20' : 'bg-slate-50 dark:bg-navy-800' }}">
        <div class="flex items-center space-x-3">
            <div class="avatar size-10">
                <div class="is-initial rounded-full bg-pink-600 text-white">
                    <i class="fab fa-instagram text-lg"></i>
                </div>
            </div>
            <div>
                <h4 class="font-medium">Instagram Connection</h4>
                <p class="text-sm {{ $isConnected ? 'text-success' : 'text-slate-400 dark:text-navy-300' }}">
                    {{ $isConnected ? 'Connected and Active' : 'Not Connected' }}
                </p>
            </div>
        </div>
        @if($isConnected)
            <div class="badge bg-success/10 text-success dark:bg-success/15">
                <div class="size-2 rounded-full bg-success mr-1"></div>
                Connected
            </div>
        @endif
    </div>

    <!-- Account Configuration -->
    <div class="space-y-4">
        <h4 class="font-medium text-slate-700 dark:text-navy-100">Account Configuration</h4>
        
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <label class="block">
                <span>Username <span class="text-error">*</span></span>
                <input wire:model="username"
                    class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                    placeholder="your_instagram_username" type="text" />
                @error('username') <span class="text-xs text-error">{{ $message }}</span> @enderror
            </label>

            <label class="block">
                <span>Access Token <span class="text-error">*</span></span>
                <input wire:model="accessToken"
                    class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                    placeholder="Your Instagram Access Token" type="password" />
                @error('accessToken') <span class="text-xs text-error">{{ $message }}</span> @enderror
            </label>
        </div>
    </div>

    <!-- Automation Settings -->
    <div class="space-y-4">
        <h4 class="font-medium text-slate-700 dark:text-navy-100">Automation Settings</h4>
        
        <div class="space-y-2">
            <label class="inline-flex items-center space-x-2">
                <input wire:model="autoPost"
                    class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                    type="checkbox" />
                <span>Enable Auto Posting</span>
            </label>

            <label class="inline-flex items-center space-x-2">
                <input wire:model="autoLike"
                    class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                    type="checkbox" />
                <span>Enable Auto Like</span>
            </label>

            <label class="inline-flex items-center space-x-2">
                <input wire:model="autoFollow"
                    class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                    type="checkbox" />
                <span>Enable Auto Follow</span>
            </label>

            <label class="inline-flex items-center space-x-2">
                <input wire:model="autoComment"
                    class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                    type="checkbox" />
                <span>Enable Auto Comment</span>
            </label>

            <label class="inline-flex items-center space-x-2">
                <input wire:model="enableStoryViewing"
                    class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                    type="checkbox" />
                <span>Enable Story Viewing</span>
            </label>

            <label class="inline-flex items-center space-x-2">
                <input wire:model="enableDMAutoReply"
                    class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                    type="checkbox" />
                <span>Enable DM Auto Reply</span>
            </label>
        </div>
    </div>

    <!-- Rate Limits -->
    <div class="space-y-4">
        <h4 class="font-medium text-slate-700 dark:text-navy-100">Rate Limits</h4>
        
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <label class="block">
                <span>Max Likes/Hour <span class="text-error">*</span></span>
                <input wire:model="maxLikesPerHour"
                    class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                    type="number" min="1" max="100" />
                @error('maxLikesPerHour') <span class="text-xs text-error">{{ $message }}</span> @enderror
            </label>

            <label class="block">
                <span>Max Follows/Hour <span class="text-error">*</span></span>
                <input wire:model="maxFollowsPerHour"
                    class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                    type="number" min="1" max="50" />
                @error('maxFollowsPerHour') <span class="text-xs text-error">{{ $message }}</span> @enderror
            </label>

            <label class="block">
                <span>Max Comments/Hour <span class="text-error">*</span></span>
                <input wire:model="maxCommentsPerHour"
                    class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                    type="number" min="1" max="30" />
                @error('maxCommentsPerHour') <span class="text-xs text-error">{{ $message }}</span> @enderror
            </label>
        </div>

        <label class="block">
            <span>Unfollow After Days <span class="text-error">*</span></span>
            <input wire:model="unfollowAfterDays"
                class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                type="number" min="1" max="30" />
            @error('unfollowAfterDays') <span class="text-xs text-error">{{ $message }}</span> @enderror
        </label>
    </div>

    <!-- Content Settings -->
    <div class="space-y-4">
        <h4 class="font-medium text-slate-700 dark:text-navy-100">Content Settings</h4>
        
        <label class="block">
            <span>Target Hashtags</span>
            <textarea wire:model="targetHashtags"
                class="form-textarea mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                rows="3" placeholder="#hashtag1 #hashtag2 #hashtag3"></textarea>
            @error('targetHashtags') <span class="text-xs text-error">{{ $message }}</span> @enderror
        </label>

        <label class="block">
            <span>Comment Templates</span>
            <textarea wire:model="commentTemplates"
                class="form-textarea mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                rows="4" placeholder="Great post! | Amazing content! | Love this!"></textarea>
            @error('commentTemplates') <span class="text-xs text-error">{{ $message }}</span> @enderror
        </label>

        @if($enableDMAutoReply)
            <label class="block">
                <span>DM Auto Reply Message</span>
                <textarea wire:model="dmAutoReplyMessage"
                    class="form-textarea mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                    rows="3" placeholder="Thank you for your message!"></textarea>
                @error('dmAutoReplyMessage') <span class="text-xs text-error">{{ $message }}</span> @enderror
            </label>
        @endif
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-between pt-4 border-t border-slate-200 dark:border-navy-500">
        <div class="flex space-x-2">
            @if($isConnected)
                <button wire:click="testConnection"
                    class="btn border border-slate-300 text-slate-700 hover:bg-slate-150 focus:bg-slate-150 active:bg-slate-150/80 dark:border-navy-450 dark:text-navy-100 dark:hover:bg-navy-500 dark:focus:bg-navy-500 dark:active:bg-navy-500/90">
                    Test Connection
                </button>
                <button wire:click="disconnect"
                    class="btn border border-error text-error hover:bg-error/10 focus:bg-error/10 active:bg-error/20">
                    Disconnect
                </button>
            @endif
        </div>
        
        <div class="flex space-x-2">
            <button wire:click="save"
                class="btn bg-primary font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90 dark:bg-accent dark:hover:bg-accent-focus dark:focus:bg-accent-focus dark:active:bg-accent/90">
                {{ $isConnected ? 'Update Settings' : 'Connect Instagram' }}
            </button>
        </div>
    </div>
</div>
