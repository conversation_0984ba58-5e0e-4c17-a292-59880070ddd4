// Standalone preloader function
const hidePreloader = () => {
  const preloader = document.querySelector(".app-preloader");
  if (!preloader) return;

  // Avoid hiding if already being hidden
  if (preloader.classList.contains("animate-[cubic-bezier(0.4,0,0.2,1)_fade-out_500ms_forwards]")) {
    return;
  }

  setTimeout(() => {
    preloader.classList.add(
      "animate-[cubic-bezier(0.4,0,0.2,1)_fade-out_500ms_forwards]"
    );
    setTimeout(() => {
      if (preloader.parentNode) {
        preloader.remove();
      }
    }, 1000);
  }, 150);
};

export default () => ({
  isDarkModeEnabled: Alpine.$persist(false).as("_x_darkMode_on"),
  isSearchbarActive: false,
  isSidebarExpanded: false,
  isRightSidebarExpanded: false,

  init() {
    this.isSidebarExpanded =
      document.querySelector(".sidebar") &&
      document.body.classList.contains("is-sidebar-open") &&
      Alpine.store("breakpoints").xlAndUp;

    Alpine.effect(() => {
      this.isDarkModeEnabled
        ? document.documentElement.classList.add("dark")
        : document.documentElement.classList.remove("dark");
    });

    Alpine.effect(() => {
      this.isSidebarExpanded
        ? document.body.classList.add("is-sidebar-open")
        : document.body.classList.remove("is-sidebar-open");
    });

    Alpine.effect(() => {
      if (Alpine.store("breakpoints").smAndUp) this.isSearchbarActive = false;
    });

    window.addEventListener('changed:breakpoint', () => {
      if (this.isSidebarExpanded) this.isSidebarExpanded = false;
      if (this.isRightSidebarExpanded) this.isRightSidebarExpanded = false;
    })
  },

  documentBody: {
    ["@load.window"]() {
      hidePreloader();
    },
    ["@livewire:navigating.window"]() {
      // Apply dark mode immediately before navigation starts
      if (localStorage.getItem("_x_darkMode_on") === "true") {
        document.documentElement.classList.add("dark");
      } else {
        document.documentElement.classList.remove("dark");
      }
    },
    ["@livewire:navigated.window"]() {
      hidePreloader();
      // Apply dark mode again after navigation completes
      if (localStorage.getItem("_x_darkMode_on") === "true") {
        document.documentElement.classList.add("dark");
      } else {
        document.documentElement.classList.remove("dark");
      }
    },
  },

  hidePreloader,
});
