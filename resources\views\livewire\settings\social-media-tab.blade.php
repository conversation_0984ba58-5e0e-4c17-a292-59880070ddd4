<div class="card">
    <div class="flex flex-col items-center space-y-4 border-b border-slate-200 p-4 dark:border-navy-500 sm:flex-row sm:justify-between sm:space-y-0 sm:px-5">
        <h2 class="text-lg font-medium tracking-wide text-slate-700 dark:text-navy-100">
            Social Media Settings
        </h2>
        <div class="flex justify-center space-x-2">
            <button wire:click="cancel"
                class="btn min-w-[7rem] rounded-full border border-slate-300 font-medium text-slate-700 hover:bg-slate-150 focus:bg-slate-150 active:bg-slate-150/80 dark:border-navy-450 dark:text-navy-100 dark:hover:bg-navy-500 dark:focus:bg-navy-500 dark:active:bg-navy-500/90">
                Cancel
            </button>
            <button wire:click="save"
                class="btn min-w-[7rem] rounded-full bg-primary font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90 dark:bg-accent dark:hover:bg-accent-focus dark:focus:bg-accent-focus dark:active:bg-accent/90">
                Save
            </button>
        </div>
    </div>

    <div class="p-4 sm:p-5">
        <!-- Flash Messages -->
        @if (session()->has('message'))
            <div class="alert mb-4 flex rounded-lg border border-success bg-success/10 py-4 px-4 text-success sm:px-5">
                <svg xmlns="http://www.w3.org/2000/svg" class="size-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                <div class="ml-2">
                    <h3 class="text-sm font-medium">{{ session('message') }}</h3>
                </div>
            </div>
        @endif

        <!-- Social Media Profiles -->
        <div class="space-y-6">
            <div>
                <h3 class="text-base font-medium text-slate-600 dark:text-navy-100 mb-4">
                    Social Media Profiles
                </h3>
                
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <!-- Facebook -->
                    <label class="block">
                        <span class="flex items-center space-x-2">
                            <i class="fab fa-facebook text-blue-600"></i>
                            <span>Facebook URL</span>
                        </span>
                        <span class="relative mt-1.5 flex">
                            <input wire:model="facebookUrl"
                                class="form-input peer w-full rounded-full border border-slate-300 bg-transparent px-3 py-2 pl-9 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                                placeholder="https://facebook.com/username" type="url" />
                            <span class="pointer-events-none absolute flex h-full w-10 items-center justify-center text-slate-400 peer-focus:text-primary dark:text-navy-300 dark:peer-focus:text-accent">
                                <i class="fab fa-facebook text-base"></i>
                            </span>
                        </span>
                        @error('facebookUrl') <span class="text-xs text-error">{{ $message }}</span> @enderror
                    </label>

                    <!-- Twitter -->
                    <label class="block">
                        <span class="flex items-center space-x-2">
                            <i class="fab fa-twitter text-blue-400"></i>
                            <span>Twitter URL</span>
                        </span>
                        <span class="relative mt-1.5 flex">
                            <input wire:model="twitterUrl"
                                class="form-input peer w-full rounded-full border border-slate-300 bg-transparent px-3 py-2 pl-9 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                                placeholder="https://twitter.com/username" type="url" />
                            <span class="pointer-events-none absolute flex h-full w-10 items-center justify-center text-slate-400 peer-focus:text-primary dark:text-navy-300 dark:peer-focus:text-accent">
                                <i class="fab fa-twitter text-base"></i>
                            </span>
                        </span>
                        @error('twitterUrl') <span class="text-xs text-error">{{ $message }}</span> @enderror
                    </label>

                    <!-- Instagram -->
                    <label class="block">
                        <span class="flex items-center space-x-2">
                            <i class="fab fa-instagram text-pink-600"></i>
                            <span>Instagram URL</span>
                        </span>
                        <span class="relative mt-1.5 flex">
                            <input wire:model="instagramUrl"
                                class="form-input peer w-full rounded-full border border-slate-300 bg-transparent px-3 py-2 pl-9 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                                placeholder="https://instagram.com/username" type="url" />
                            <span class="pointer-events-none absolute flex h-full w-10 items-center justify-center text-slate-400 peer-focus:text-primary dark:text-navy-300 dark:peer-focus:text-accent">
                                <i class="fab fa-instagram text-base"></i>
                            </span>
                        </span>
                        @error('instagramUrl') <span class="text-xs text-error">{{ $message }}</span> @enderror
                    </label>

                    <!-- LinkedIn -->
                    <label class="block">
                        <span class="flex items-center space-x-2">
                            <i class="fab fa-linkedin text-blue-700"></i>
                            <span>LinkedIn URL</span>
                        </span>
                        <span class="relative mt-1.5 flex">
                            <input wire:model="linkedinUrl"
                                class="form-input peer w-full rounded-full border border-slate-300 bg-transparent px-3 py-2 pl-9 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                                placeholder="https://linkedin.com/in/username" type="url" />
                            <span class="pointer-events-none absolute flex h-full w-10 items-center justify-center text-slate-400 peer-focus:text-primary dark:text-navy-300 dark:peer-focus:text-accent">
                                <i class="fab fa-linkedin text-base"></i>
                            </span>
                        </span>
                        @error('linkedinUrl') <span class="text-xs text-error">{{ $message }}</span> @enderror
                    </label>

                    <!-- YouTube -->
                    <label class="block">
                        <span class="flex items-center space-x-2">
                            <i class="fab fa-youtube text-red-600"></i>
                            <span>YouTube URL</span>
                        </span>
                        <span class="relative mt-1.5 flex">
                            <input wire:model="youtubeUrl"
                                class="form-input peer w-full rounded-full border border-slate-300 bg-transparent px-3 py-2 pl-9 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                                placeholder="https://youtube.com/c/username" type="url" />
                            <span class="pointer-events-none absolute flex h-full w-10 items-center justify-center text-slate-400 peer-focus:text-primary dark:text-navy-300 dark:peer-focus:text-accent">
                                <i class="fab fa-youtube text-base"></i>
                            </span>
                        </span>
                        @error('youtubeUrl') <span class="text-xs text-error">{{ $message }}</span> @enderror
                    </label>

                    <!-- TikTok -->
                    <label class="block">
                        <span class="flex items-center space-x-2">
                            <i class="fab fa-tiktok text-black dark:text-white"></i>
                            <span>TikTok URL</span>
                        </span>
                        <span class="relative mt-1.5 flex">
                            <input wire:model="tiktokUrl"
                                class="form-input peer w-full rounded-full border border-slate-300 bg-transparent px-3 py-2 pl-9 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                                placeholder="https://tiktok.com/@username" type="url" />
                            <span class="pointer-events-none absolute flex h-full w-10 items-center justify-center text-slate-400 peer-focus:text-primary dark:text-navy-300 dark:peer-focus:text-accent">
                                <i class="fab fa-tiktok text-base"></i>
                            </span>
                        </span>
                        @error('tiktokUrl') <span class="text-xs text-error">{{ $message }}</span> @enderror
                    </label>
                </div>
            </div>

            <div class="my-7 h-px bg-slate-200 dark:bg-navy-500"></div>

            <!-- Social Media Settings -->
            <div>
                <h3 class="text-base font-medium text-slate-600 dark:text-navy-100 mb-4">
                    Social Media Preferences
                </h3>
                
                <div class="space-y-4">
                    <label class="inline-flex items-center space-x-2">
                        <input wire:model="autoPost"
                            class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                            type="checkbox" />
                        <span>Enable auto-posting to social media</span>
                    </label>

                    <label class="inline-flex items-center space-x-2">
                        <input wire:model="shareNotifications"
                            class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                            type="checkbox" />
                        <span>Receive notifications for social media activity</span>
                    </label>

                    <label class="inline-flex items-center space-x-2">
                        <input wire:model="crossPosting"
                            class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                            type="checkbox" />
                        <span>Enable cross-posting between platforms</span>
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>
