<div class="card">
    <div class="flex flex-col items-center space-y-4 border-b border-slate-200 p-4 dark:border-navy-500 sm:flex-row sm:justify-between sm:space-y-0 sm:px-5">
        <h2 class="text-lg font-medium tracking-wide text-slate-700 dark:text-navy-100">
            AI Bots Configuration
        </h2>
        <div class="flex justify-center space-x-2">
            <button wire:click="resetToDefaults"
                class="btn min-w-[7rem] rounded-full border border-slate-300 font-medium text-slate-700 hover:bg-slate-150 focus:bg-slate-150 active:bg-slate-150/80 dark:border-navy-450 dark:text-navy-100 dark:hover:bg-navy-500 dark:focus:bg-navy-500 dark:active:bg-navy-500/90">
                Reset
            </button>
            <button wire:click="cancel"
                class="btn min-w-[7rem] rounded-full border border-slate-300 font-medium text-slate-700 hover:bg-slate-150 focus:bg-slate-150 active:bg-slate-150/80 dark:border-navy-450 dark:text-navy-100 dark:hover:bg-navy-500 dark:focus:bg-navy-500 dark:active:bg-navy-500/90">
                Cancel
            </button>
            <button wire:click="save"
                class="btn min-w-[7rem] rounded-full bg-primary font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90 dark:bg-accent dark:hover:bg-accent-focus dark:focus:bg-accent-focus dark:active:bg-accent/90">
                Save
            </button>
        </div>
    </div>

    <div class="p-4 sm:p-5">
        <!-- Flash Messages -->
        @if (session()->has('message'))
            <div class="alert mb-4 flex rounded-lg border border-success bg-success/10 py-4 px-4 text-success sm:px-5">
                <svg xmlns="http://www.w3.org/2000/svg" class="size-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                <div class="ml-2">
                    <h3 class="text-sm font-medium">{{ session('message') }}</h3>
                </div>
            </div>
        @endif

        <div class="space-y-6">
            <!-- API Keys Section -->
            <div>
                <h3 class="text-base font-medium text-slate-600 dark:text-navy-100 mb-4">
                    API Keys Configuration
                </h3>
                
                <div class="space-y-4">
                    <!-- OpenAI API Key -->
                    <label class="block">
                        <span class="flex items-center space-x-2">
                            <span>OpenAI API Key</span>
                            <button wire:click="testConnection('openai')" class="text-xs text-primary hover:text-primary-focus">Test</button>
                        </span>
                        <span class="relative mt-1.5 flex">
                            <input wire:model="openaiApiKey"
                                class="form-input peer w-full rounded-full border border-slate-300 bg-transparent px-3 py-2 pl-9 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                                placeholder="sk-..." type="password" />
                            <span class="pointer-events-none absolute flex h-full w-10 items-center justify-center text-slate-400 peer-focus:text-primary dark:text-navy-300 dark:peer-focus:text-accent">
                                <i class="fa fa-key text-base"></i>
                            </span>
                        </span>
                        @error('openaiApiKey') <span class="text-xs text-error">{{ $message }}</span> @enderror
                    </label>

                    <!-- Claude API Key -->
                    <label class="block">
                        <span class="flex items-center space-x-2">
                            <span>Claude API Key</span>
                            <button wire:click="testConnection('claude')" class="text-xs text-primary hover:text-primary-focus">Test</button>
                        </span>
                        <span class="relative mt-1.5 flex">
                            <input wire:model="claudeApiKey"
                                class="form-input peer w-full rounded-full border border-slate-300 bg-transparent px-3 py-2 pl-9 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                                placeholder="sk-ant-..." type="password" />
                            <span class="pointer-events-none absolute flex h-full w-10 items-center justify-center text-slate-400 peer-focus:text-primary dark:text-navy-300 dark:peer-focus:text-accent">
                                <i class="fa fa-key text-base"></i>
                            </span>
                        </span>
                        @error('claudeApiKey') <span class="text-xs text-error">{{ $message }}</span> @enderror
                    </label>

                    <!-- Gemini API Key -->
                    <label class="block">
                        <span class="flex items-center space-x-2">
                            <span>Gemini API Key</span>
                            <button wire:click="testConnection('gemini')" class="text-xs text-primary hover:text-primary-focus">Test</button>
                        </span>
                        <span class="relative mt-1.5 flex">
                            <input wire:model="geminiApiKey"
                                class="form-input peer w-full rounded-full border border-slate-300 bg-transparent px-3 py-2 pl-9 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                                placeholder="AIza..." type="password" />
                            <span class="pointer-events-none absolute flex h-full w-10 items-center justify-center text-slate-400 peer-focus:text-primary dark:text-navy-300 dark:peer-focus:text-accent">
                                <i class="fa fa-key text-base"></i>
                            </span>
                        </span>
                        @error('geminiApiKey') <span class="text-xs text-error">{{ $message }}</span> @enderror
                    </label>
                </div>
            </div>

            <div class="my-7 h-px bg-slate-200 dark:bg-navy-500"></div>

            <!-- Model Configuration -->
            <div>
                <h3 class="text-base font-medium text-slate-600 dark:text-navy-100 mb-4">
                    Model Configuration
                </h3>
                
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <!-- Default Model -->
                    <label class="block">
                        <span>Default Model <span class="text-error">*</span></span>
                        <select wire:model="defaultModel"
                            class="form-select mt-1.5 w-full rounded-lg border border-slate-300 bg-white px-3 py-2 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:bg-navy-700 dark:hover:border-navy-400 dark:focus:border-accent">
                            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                            <option value="gpt-4">GPT-4</option>
                            <option value="gpt-4-turbo">GPT-4 Turbo</option>
                            <option value="claude-3-haiku">Claude 3 Haiku</option>
                            <option value="claude-3-sonnet">Claude 3 Sonnet</option>
                            <option value="claude-3-opus">Claude 3 Opus</option>
                            <option value="gemini-pro">Gemini Pro</option>
                        </select>
                        @error('defaultModel') <span class="text-xs text-error">{{ $message }}</span> @enderror
                    </label>

                    <!-- Max Tokens -->
                    <label class="block">
                        <span>Max Tokens <span class="text-error">*</span></span>
                        <input wire:model="maxTokens"
                            class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                            placeholder="1000" type="number" min="100" max="4000" />
                        @error('maxTokens') <span class="text-xs text-error">{{ $message }}</span> @enderror
                    </label>

                    <!-- Temperature -->
                    <label class="block">
                        <span>Temperature <span class="text-error">*</span></span>
                        <input wire:model="temperature"
                            class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                            placeholder="0.7" type="number" min="0" max="2" step="0.1" />
                        @error('temperature') <span class="text-xs text-error">{{ $message }}</span> @enderror
                    </label>

                    <!-- Response Language -->
                    <label class="block">
                        <span>Response Language <span class="text-error">*</span></span>
                        <select wire:model="responseLanguage"
                            class="form-select mt-1.5 w-full rounded-lg border border-slate-300 bg-white px-3 py-2 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:bg-navy-700 dark:hover:border-navy-400 dark:focus:border-accent">
                            <option value="en">English</option>
                            <option value="es">Spanish</option>
                            <option value="fr">French</option>
                            <option value="de">German</option>
                            <option value="it">Italian</option>
                            <option value="pt">Portuguese</option>
                            <option value="ru">Russian</option>
                            <option value="ja">Japanese</option>
                            <option value="ko">Korean</option>
                            <option value="zh">Chinese</option>
                        </select>
                        @error('responseLanguage') <span class="text-xs text-error">{{ $message }}</span> @enderror
                    </label>
                </div>
            </div>

            <div class="my-7 h-px bg-slate-200 dark:bg-navy-500"></div>

            <!-- Bot Personality -->
            <div>
                <h3 class="text-base font-medium text-slate-600 dark:text-navy-100 mb-4">
                    Chatbot Personality
                </h3>
                
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <!-- Bot Name -->
                    <label class="block">
                        <span>Chatbot Name <span class="text-error">*</span></span>
                        <input wire:model="chatBotName"
                            class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                            placeholder="AI Assistant" type="text" />
                        @error('chatBotName') <span class="text-xs text-error">{{ $message }}</span> @enderror
                    </label>

                    <!-- Bot Personality -->
                    <label class="block">
                        <span>Personality <span class="text-error">*</span></span>
                        <select wire:model="chatBotPersonality"
                            class="form-select mt-1.5 w-full rounded-lg border border-slate-300 bg-white px-3 py-2 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:bg-navy-700 dark:hover:border-navy-400 dark:focus:border-accent">
                            <option value="helpful">Helpful</option>
                            <option value="friendly">Friendly</option>
                            <option value="professional">Professional</option>
                            <option value="casual">Casual</option>
                            <option value="witty">Witty</option>
                            <option value="formal">Formal</option>
                        </select>
                        @error('chatBotPersonality') <span class="text-xs text-error">{{ $message }}</span> @enderror
                    </label>
                </div>
            </div>

            <div class="my-7 h-px bg-slate-200 dark:bg-navy-500"></div>

            <!-- Feature Toggles -->
            <div>
                <h3 class="text-base font-medium text-slate-600 dark:text-navy-100 mb-4">
                    Feature Settings
                </h3>
                
                <div class="space-y-4">
                    <label class="inline-flex items-center space-x-2">
                        <input wire:model="enableChatBot"
                            class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                            type="checkbox" />
                        <span>Enable AI Chatbot</span>
                    </label>

                    <label class="inline-flex items-center space-x-2">
                        <input wire:model="enableAutoResponses"
                            class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                            type="checkbox" />
                        <span>Enable Auto Responses</span>
                    </label>

                    <label class="inline-flex items-center space-x-2">
                        <input wire:model="enableContentGeneration"
                            class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                            type="checkbox" />
                        <span>Enable Content Generation</span>
                    </label>

                    <label class="inline-flex items-center space-x-2">
                        <input wire:model="enableImageGeneration"
                            class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                            type="checkbox" />
                        <span>Enable Image Generation</span>
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>
