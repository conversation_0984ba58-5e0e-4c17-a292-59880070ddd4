<?php

namespace App\Livewire\Settings\Socials;

use Livewire\Component;

class InstagramSettings extends Component
{
    public $isConnected = false;
    public $username = '';
    public $accessToken = '';
    public $autoPost = false;
    public $autoLike = false;
    public $autoFollow = false;
    public $autoComment = false;
    public $maxLikesPerHour = 30;
    public $maxFollowsPerHour = 20;
    public $maxCommentsPerHour = 10;
    public $targetHashtags = '';
    public $commentTemplates = '';
    public $unfollowAfterDays = 7;
    public $enableStoryViewing = true;
    public $enableDMAutoReply = false;
    public $dmAutoReplyMessage = 'Thank you for your message! We will get back to you soon.';

    public function mount()
    {
        // Load existing Instagram settings
        $user = auth()->user();
        if ($user) {
            $this->username = $user->instagram_username ?? '';
            $this->accessToken = $user->instagram_access_token ?? '';
            $this->autoPost = $user->instagram_auto_post ?? false;
            $this->autoLike = $user->instagram_auto_like ?? false;
            $this->autoFollow = $user->instagram_auto_follow ?? false;
            $this->autoComment = $user->instagram_auto_comment ?? false;
            $this->maxLikesPerHour = $user->instagram_max_likes ?? 30;
            $this->maxFollowsPerHour = $user->instagram_max_follows ?? 20;
            $this->maxCommentsPerHour = $user->instagram_max_comments ?? 10;
            $this->targetHashtags = $user->instagram_target_hashtags ?? '';
            $this->commentTemplates = $user->instagram_comment_templates ?? '';
            $this->unfollowAfterDays = $user->instagram_unfollow_days ?? 7;
            $this->enableStoryViewing = $user->instagram_story_viewing ?? true;
            $this->enableDMAutoReply = $user->instagram_dm_auto_reply ?? false;
            $this->dmAutoReplyMessage = $user->instagram_dm_message ?? 'Thank you for your message! We will get back to you soon.';
            
            $this->isConnected = !empty($this->username) && !empty($this->accessToken);
        }
    }

    public function save()
    {
        $this->validate([
            'username' => 'required|string|max:50',
            'accessToken' => 'required|string',
            'maxLikesPerHour' => 'required|integer|min:1|max:100',
            'maxFollowsPerHour' => 'required|integer|min:1|max:50',
            'maxCommentsPerHour' => 'required|integer|min:1|max:30',
            'targetHashtags' => 'nullable|string|max:1000',
            'commentTemplates' => 'nullable|string|max:2000',
            'unfollowAfterDays' => 'required|integer|min:1|max:30',
            'dmAutoReplyMessage' => 'nullable|string|max:500',
        ]);

        // Save Instagram settings
        $user = auth()->user();
        if ($user) {
            $user->update([
                'instagram_username' => $this->username,
                'instagram_access_token' => $this->accessToken,
                'instagram_auto_post' => $this->autoPost,
                'instagram_auto_like' => $this->autoLike,
                'instagram_auto_follow' => $this->autoFollow,
                'instagram_auto_comment' => $this->autoComment,
                'instagram_max_likes' => $this->maxLikesPerHour,
                'instagram_max_follows' => $this->maxFollowsPerHour,
                'instagram_max_comments' => $this->maxCommentsPerHour,
                'instagram_target_hashtags' => $this->targetHashtags,
                'instagram_comment_templates' => $this->commentTemplates,
                'instagram_unfollow_days' => $this->unfollowAfterDays,
                'instagram_story_viewing' => $this->enableStoryViewing,
                'instagram_dm_auto_reply' => $this->enableDMAutoReply,
                'instagram_dm_message' => $this->dmAutoReplyMessage,
            ]);
        }

        $this->isConnected = true;
        session()->flash('message', 'Instagram settings saved successfully!');
        $this->dispatch('close-modal');
    }

    public function testConnection()
    {
        // Test Instagram API connection
        session()->flash('message', 'Testing Instagram connection...');
    }

    public function disconnect()
    {
        $this->username = '';
        $this->accessToken = '';
        $this->isConnected = false;
        
        // Update database
        $user = auth()->user();
        if ($user) {
            $user->update([
                'instagram_username' => null,
                'instagram_access_token' => null,
            ]);
        }
        
        session()->flash('message', 'Instagram disconnected successfully!');
        $this->dispatch('close-modal');
    }

    public function render()
    {
        return view('livewire.settings.socials.instagram-settings');
    }
}
