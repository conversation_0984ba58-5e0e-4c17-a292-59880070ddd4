<?php

namespace App\Livewire\Settings\Socials;

use Livewire\Component;

class WhatsappSettings extends Component
{
    public $isConnected = false;
    public $phoneNumber = '';
    public $apiKey = '';
    public $webhookUrl = '';
    public $autoReply = false;
    public $businessHours = true;
    public $startTime = '09:00';
    public $endTime = '17:00';
    public $welcomeMessage = 'Hello! Welcome to our WhatsApp service.';
    public $awayMessage = 'We are currently away. We will get back to you soon.';
    public $maxMessagesPerDay = 100;
    public $enableGroupMessages = false;
    public $enableBroadcast = true;

    public function mount()
    {
        // Load existing WhatsApp settings
        $user = auth()->user();
        if ($user) {
            $this->phoneNumber = $user->whatsapp_phone ?? '';
            $this->apiKey = $user->whatsapp_api_key ?? '';
            $this->webhookUrl = $user->whatsapp_webhook_url ?? '';
            $this->autoReply = $user->whatsapp_auto_reply ?? false;
            $this->businessHours = $user->whatsapp_business_hours ?? true;
            $this->startTime = $user->whatsapp_start_time ?? '09:00';
            $this->endTime = $user->whatsapp_end_time ?? '17:00';
            $this->welcomeMessage = $user->whatsapp_welcome_message ?? 'Hello! Welcome to our WhatsApp service.';
            $this->awayMessage = $user->whatsapp_away_message ?? 'We are currently away. We will get back to you soon.';
            $this->maxMessagesPerDay = $user->whatsapp_max_messages ?? 100;
            $this->enableGroupMessages = $user->whatsapp_group_messages ?? false;
            $this->enableBroadcast = $user->whatsapp_broadcast ?? true;

            $this->isConnected = !empty($this->phoneNumber) && !empty($this->apiKey);
        }
    }

    public function save()
    {
        $this->validate([
            'phoneNumber' => 'required|string|max:20',
            'apiKey' => 'required|string',
            'webhookUrl' => 'nullable|url',
            'startTime' => 'required|date_format:H:i',
            'endTime' => 'required|date_format:H:i',
            'welcomeMessage' => 'required|string|max:500',
            'awayMessage' => 'required|string|max:500',
            'maxMessagesPerDay' => 'required|integer|min:1|max:1000',
        ]);

        // Save WhatsApp settings
        $user = auth()->user();
        if ($user) {
            $user->update([
                'whatsapp_phone' => $this->phoneNumber,
                'whatsapp_api_key' => $this->apiKey,
                'whatsapp_webhook_url' => $this->webhookUrl,
                'whatsapp_auto_reply' => $this->autoReply,
                'whatsapp_business_hours' => $this->businessHours,
                'whatsapp_start_time' => $this->startTime,
                'whatsapp_end_time' => $this->endTime,
                'whatsapp_welcome_message' => $this->welcomeMessage,
                'whatsapp_away_message' => $this->awayMessage,
                'whatsapp_max_messages' => $this->maxMessagesPerDay,
                'whatsapp_group_messages' => $this->enableGroupMessages,
                'whatsapp_broadcast' => $this->enableBroadcast,
            ]);
        }

        $this->isConnected = true;
        session()->flash('message', 'WhatsApp settings saved successfully!');
    }

    public function testConnection()
    {
        // Test WhatsApp API connection
        session()->flash('message', 'Testing WhatsApp connection...');
    }

    public function disconnect()
    {
        $this->phoneNumber = '';
        $this->apiKey = '';
        $this->webhookUrl = '';
        $this->isConnected = false;

        // Update database
        $user = auth()->user();
        if ($user) {
            $user->update([
                'whatsapp_phone' => null,
                'whatsapp_api_key' => null,
                'whatsapp_webhook_url' => null,
            ]);
        }

        session()->flash('message', 'WhatsApp disconnected successfully!');
    }

    public function render()
    {
        return view('livewire.settings.socials.whatsapp-settings');
    }
}
