<div class="contents">
    <div class="space-y-6">
        <!-- Flash Messages -->
        <!--[if BLOCK]><![endif]--><?php if(session()->has('message')): ?>
            <div class="alert flex rounded-lg border border-success bg-success/10 py-4 px-4 text-success sm:px-5">
                <svg xmlns="http://www.w3.org/2000/svg" class="size-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd" />
                </svg>
                <div class="ml-2">
                    <h3 class="text-sm font-medium"><?php echo e(session('message')); ?></h3>
                </div>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        <!-- Social Media Platforms Grid -->
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 sm:gap-5 lg:gap-6">
            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $socialPlatforms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $platform): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="card p-4 sm:p-5 shadow-lg hover:shadow-xl transition-shadow duration-300 ease-in-out">
                    <div class="flex items-start justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="avatar size-12">
                                <div class="is-initial rounded-full <?php echo e($platform['color']); ?> text-white">
                                    <i class="<?php echo e($platform['icon']); ?> text-xl"></i>
                                </div>
                            </div>
                            <div>
                                <h3 class="text-base font-medium tracking-wide text-slate-700 dark:text-navy-100">
                                    <?php echo e($platform['name']); ?>

                                </h3>
                                <div class="flex items-center space-x-2 mt-1">
                                    <!--[if BLOCK]><![endif]--><?php if($platform['connected']): ?>
                                        <div class="badge bg-success/10 text-success dark:bg-success/15">
                                            <div class="size-2 rounded-full bg-success mr-1"></div>
                                            Connected
                                        </div>
                                    <?php else: ?>
                                        <div
                                            class="badge bg-slate-150 text-slate-600 dark:bg-navy-500 dark:text-navy-200">
                                            <div class="size-2 rounded-full bg-slate-400 mr-1"></div>
                                            Not Connected
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                        </div>
                        <button wire:click="openPlatformSettings('<?php echo e($key); ?>')"
                            class="btn size-8 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                            <svg xmlns="http://www.w3.org/2000/svg" class="size-4" viewBox="0 0 20 20"
                                fill="currentColor">
                                <path fill-rule="evenodd"
                                    d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
                                    clip-rule="evenodd" />
                            </svg>
                        </button>
                    </div>

                    <p class="mt-3 text-sm text-slate-400 dark:text-navy-300">
                        <?php echo e($platform['description']); ?>

                    </p>

                    <div class="mt-4 flex items-center justify-between">
                        <!--[if BLOCK]><![endif]--><?php if($platform['connected']): ?>
                            <span class="text-xs text-success font-medium">
                                <i class="fa fa-check-circle mr-1"></i>
                                Active & Running
                            </span>
                        <?php else: ?>
                            <span class="text-xs text-slate-400 dark:text-navy-300">
                                <i class="fa fa-circle mr-1"></i>
                                Ready to Connect
                            </span>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <button wire:click="openPlatformSettings('<?php echo e($key); ?>')"
                            class="text-xs text-primary hover:text-primary-focus dark:text-accent-light dark:hover:text-accent font-medium">
                            <?php echo e($platform['connected'] ? 'Configure' : 'Setup'); ?>

                        </button>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
        </div>

        <!-- General Settings Card -->
        <div class="card">
            <div
                class="flex flex-col items-center space-y-4 border-b border-slate-200 p-4 dark:border-navy-500 sm:flex-row sm:justify-between sm:space-y-0 sm:px-5">
                <h2 class="text-lg font-medium tracking-wide text-slate-700 dark:text-navy-100">
                    General Social Media Settings
                </h2>
                <div class="flex justify-center space-x-2">
                    <button wire:click="cancel"
                        class="btn min-w-[7rem] rounded-full border border-slate-300 font-medium text-slate-700 hover:bg-slate-150 focus:bg-slate-150 active:bg-slate-150/80 dark:border-navy-450 dark:text-navy-100 dark:hover:bg-navy-500 dark:focus:bg-navy-500 dark:active:bg-navy-500/90">
                        Cancel
                    </button>
                    <button wire:click="save"
                        class="btn min-w-[7rem] rounded-full bg-primary font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90 dark:bg-accent dark:hover:bg-accent-focus dark:focus:bg-accent-focus dark:active:bg-accent/90">
                        Save
                    </button>
                </div>
            </div>

            <div class="p-4 sm:p-5">
                <div class="space-y-4">
                    <label class="inline-flex items-center space-x-2">
                        <input wire:model="autoPost"
                            class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                            type="checkbox" />
                        <span>Enable auto-posting to social media</span>
                    </label>

                    <label class="inline-flex items-center space-x-2">
                        <input wire:model="shareNotifications"
                            class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                            type="checkbox" />
                        <span>Receive notifications for social media activity</span>
                    </label>

                    <label class="inline-flex items-center space-x-2">
                        <input wire:model="crossPosting"
                            class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                            type="checkbox" />
                        <span>Enable cross-posting between platforms</span>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <!-- Platform Settings Modal -->
    <!--[if BLOCK]><![endif]--><?php if($showModal): ?>
        <div class="fixed inset-0 z-[100] flex flex-col items-center justify-center overflow-hidden px-4 py-6 sm:px-5"
            role="dialog"
            wire:keydown.escape="closeModal">

            <div class="absolute inset-0 bg-slate-900/60 backdrop-blur-sm transition-opacity duration-300"
                wire:click="closeModal"></div>

            <div class="relative w-full max-w-4xl max-h-[90vh] overflow-y-auto rounded-lg bg-white px-4 py-6 transition-all duration-300 dark:bg-navy-700 sm:px-6">

                <div class="flex items-center justify-between border-b border-slate-200 pb-4 dark:border-navy-500">
                    <h3 class="text-lg font-medium text-slate-700 dark:text-navy-100">
                        <!--[if BLOCK]><![endif]--><?php switch($currentPlatform):
                            case ('whatsapp'): ?> WhatsApp Settings <?php break; ?>
                            <?php case ('instagram'): ?> Instagram Settings <?php break; ?>
                            <?php case ('telegram'): ?> Telegram Settings <?php break; ?>
                            <?php case ('discord'): ?> Discord Settings <?php break; ?>
                            <?php case ('facebook'): ?> Facebook Settings <?php break; ?>
                            <?php case ('snapchat'): ?> Snapchat Settings <?php break; ?>
                            <?php default: ?> Platform Settings
                        <?php endswitch; ?><!--[if ENDBLOCK]><![endif]-->
                    </h3>
                    <button wire:click="closeModal" class="btn size-8 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                        <svg xmlns="http://www.w3.org/2000/svg" class="size-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>

                <div class="mt-4">
                    <!--[if BLOCK]><![endif]--><?php switch($currentPlatform):
                        case ('whatsapp'): ?>
                            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('settings.socials.whatsapp-settings', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-2104898728-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                            <?php break; ?>
                        <?php case ('instagram'): ?>
                            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('settings.socials.instagram-settings', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-2104898728-1', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                            <?php break; ?>
                        <?php case ('telegram'): ?>
                            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('settings.socials.telegram-settings', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-2104898728-2', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                            <?php break; ?>
                        <?php case ('discord'): ?>
                            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('settings.socials.discord-settings', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-2104898728-3', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                            <?php break; ?>
                        <?php case ('facebook'): ?>
                            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('settings.socials.facebook-settings', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-2104898728-4', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                            <?php break; ?>
                        <?php case ('snapchat'): ?>
                            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('settings.socials.snapchat-settings', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-2104898728-5', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                            <?php break; ?>
                        <?php default: ?>
                            <p class="text-center text-slate-500 dark:text-navy-300">Please select a platform to configure.</p>
                    <?php endswitch; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>
<?php /**PATH D:\Laravel_Projects\alhars\resources\views/livewire/settings/social-media-tab.blade.php ENDPATH**/ ?>