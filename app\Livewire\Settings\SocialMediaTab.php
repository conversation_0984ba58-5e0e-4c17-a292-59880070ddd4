<?php

namespace App\Livewire\Settings;

use Livewire\Component;

class SocialMediaTab extends Component
{
    public $autoPost = false;
    public $shareNotifications = true;
    public $crossPosting = false;

    // Social platform connection status
    public $socialPlatforms = [
        'whatsapp' => [
            'name' => 'WhatsApp',
            'icon' => 'fab fa-whatsapp',
            'color' => 'bg-green-500',
            'description' => 'Automate WhatsApp messaging and customer support',
            'connected' => false,
        ],
        'instagram' => [
            'name' => 'Instagram',
            'icon' => 'fab fa-instagram',
            'color' => 'bg-pink-600',
            'description' => 'Manage Instagram posts, stories, and engagement',
            'connected' => false,
        ],
        'telegram' => [
            'name' => 'Telegram',
            'icon' => 'fab fa-telegram',
            'color' => 'bg-blue-500',
            'description' => 'Create and manage Telegram bots and channels',
            'connected' => false,
        ],
        'discord' => [
            'name' => 'Discord',
            'icon' => 'fab fa-discord',
            'color' => 'bg-indigo-600',
            'description' => 'Build Discord bots and server automation',
            'connected' => false,
        ],
        'facebook' => [
            'name' => 'Facebook',
            'icon' => 'fab fa-facebook',
            'color' => 'bg-blue-600',
            'description' => 'Manage Facebook pages and automated posting',
            'connected' => false,
        ],
        'snapchat' => [
            'name' => 'Snapchat',
            'icon' => 'fab fa-snapchat',
            'color' => 'bg-yellow-400',
            'description' => 'Create and share Snapchat content automatically',
            'connected' => false,
        ],
    ];

    public function mount()
    {
        // Check connection status for each platform
        $user = auth()->user();
        if ($user) {
            $this->socialPlatforms['whatsapp']['connected'] = !empty($user->whatsapp_phone) && !empty($user->whatsapp_api_key);
            $this->socialPlatforms['instagram']['connected'] = !empty($user->instagram_username) && !empty($user->instagram_access_token);
            $this->socialPlatforms['telegram']['connected'] = !empty($user->telegram_bot_token) && !empty($user->telegram_bot_username);
            $this->socialPlatforms['discord']['connected'] = !empty($user->discord_bot_token) && !empty($user->discord_client_id);
            $this->socialPlatforms['facebook']['connected'] = !empty($user->facebook_page_id) && !empty($user->facebook_access_token);
            $this->socialPlatforms['snapchat']['connected'] = !empty($user->snapchat_username) && !empty($user->snapchat_client_id);

            // Load general settings
            $this->autoPost = $user->social_auto_post ?? false;
            $this->shareNotifications = $user->social_share_notifications ?? true;
            $this->crossPosting = $user->social_cross_posting ?? false;
        }
    }

    public function save()
    {
        // Save general social media settings
        $user = auth()->user();
        if ($user) {
            $user->update([
                'social_auto_post' => $this->autoPost,
                'social_share_notifications' => $this->shareNotifications,
                'social_cross_posting' => $this->crossPosting,
            ]);
        }

        session()->flash('message', 'Social media settings updated successfully!');
    }

    public function cancel()
    {
        $this->mount(); // Reset to original values
    }

    public function openPlatformSettings($platform)
    {
        $this->dispatch('open-platform-modal', platform: $platform);
    }

    public function render()
    {
        return view('livewire.settings.social-media-tab');
    }
}
