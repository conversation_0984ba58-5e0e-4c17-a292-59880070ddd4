<?php

namespace App\Livewire\Settings;

use Livewire\Component;

class SocialMediaTab extends Component
{
    public $facebookUrl = '';
    public $twitterUrl = '';
    public $instagramUrl = '';
    public $linkedinUrl = '';
    public $youtubeUrl = '';
    public $tiktokUrl = '';

    public $autoPost = false;
    public $shareNotifications = true;
    public $crossPosting = false;

    public function mount()
    {
        // Load existing social media settings from user or settings table
        $user = auth()->user();
        if ($user) {
            // You can extend user model or create a separate settings table
            $this->facebookUrl = $user->facebook_url ?? '';
            $this->twitterUrl = $user->twitter_url ?? '';
            $this->instagramUrl = $user->instagram_url ?? '';
            $this->linkedinUrl = $user->linkedin_url ?? '';
            $this->youtubeUrl = $user->youtube_url ?? '';
            $this->tiktokUrl = $user->tiktok_url ?? '';
        }
    }

    public function save()
    {
        $this->validate([
            'facebookUrl' => 'nullable|url',
            'twitterUrl' => 'nullable|url',
            'instagramUrl' => 'nullable|url',
            'linkedinUrl' => 'nullable|url',
            'youtubeUrl' => 'nullable|url',
            'tiktokUrl' => 'nullable|url',
        ]);

        // Save social media settings
        $user = auth()->user();
        if ($user) {
            $user->update([
                'facebook_url' => $this->facebookUrl,
                'twitter_url' => $this->twitterUrl,
                'instagram_url' => $this->instagramUrl,
                'linkedin_url' => $this->linkedinUrl,
                'youtube_url' => $this->youtubeUrl,
                'tiktok_url' => $this->tiktokUrl,
            ]);
        }

        session()->flash('message', 'Social media settings updated successfully!');
    }

    public function cancel()
    {
        $this->mount(); // Reset to original values
    }

    public function connectPlatform($platform)
    {
        session()->flash('message', ucfirst($platform) . ' integration coming soon!');
    }

    public function disconnectPlatform($platform)
    {
        switch ($platform) {
            case 'facebook':
                $this->facebookUrl = '';
                break;
            case 'twitter':
                $this->twitterUrl = '';
                break;
            case 'instagram':
                $this->instagramUrl = '';
                break;
            case 'linkedin':
                $this->linkedinUrl = '';
                break;
            case 'youtube':
                $this->youtubeUrl = '';
                break;
            case 'tiktok':
                $this->tiktokUrl = '';
                break;
        }
        
        session()->flash('message', ucfirst($platform) . ' disconnected successfully!');
    }

    public function render()
    {
        return view('livewire.settings.social-media-tab');
    }
}
