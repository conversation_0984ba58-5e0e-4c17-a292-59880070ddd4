@props([
    'route' => '',
    'navPrefix' => '',
    'tooltip' => '',
    'useWireNavigate' => 'true',
    'viewBox' => '0 0 24 24'
])

@php
    // Get the current route prefix from the parent scope (passed by SidebarComposer)
    // The parent component should have $routePrefix variable available
    $currentPageRoutePrefix = isset($routePrefix) ? $routePrefix : '';

    // If $routePrefix is not available from parent, calculate it ourselves
    if (empty($currentPageRoutePrefix) && !is_null(request()->route())) {
        $pageName = request()->route()->getName();
        $currentPageRoutePrefix = explode('/', $pageName)[0] ?? '';
    }

    // Check if this nav item should be active
    // Compare the component's navPrefix prop with the current page's route prefix
    $isActive = $navPrefix === $currentPageRoutePrefix;

    $activeClasses = 'text-primary hover:bg-primary/20 focus:bg-primary/20 active:bg-primary/25 dark:bg-navy-600 bg-primary/10 dark:text-accent-light dark:hover:bg-navy-450 dark:focus:bg-navy-450 dark:active:bg-navy-450/90';
    $inactiveClasses = 'hover:bg-primary/20 focus:bg-primary/20 active:bg-primary/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25';

    $linkClasses = $isActive ? $activeClasses : $inactiveClasses;
@endphp

@if($useWireNavigate === 'true')
    <a wire:navigate href="{{ route($route) }}"
        class="flex size-11 items-center justify-center rounded-lg outline-hidden transition-colors duration-200 {{ $linkClasses }}"
        x-tooltip.placement.right="'{{ $tooltip }}'">
        <svg class="size-7" viewBox="{{ $viewBox }}" fill="none" xmlns="http://www.w3.org/2000/svg">
            {{ $slot }}
        </svg>
    </a>
@else
    <a href="{{ route($route) }}"
        class="flex size-11 items-center justify-center rounded-lg outline-hidden transition-colors duration-200 {{ $linkClasses }}"
        x-tooltip.placement.right="'{{ $tooltip }}'">
        <svg class="size-7" viewBox="{{ $viewBox }}" fill="none" xmlns="http://www.w3.org/2000/svg">
            {{ $slot }}
        </svg>
    </a>
@endif
