<?php

namespace App\Livewire\Settings;

use Livewire\Component;

class ThemingTab extends Component
{
    public $theme = 'light';
    public $primaryColor = '#3b82f6';
    public $accentColor = '#10b981';
    public $sidebarStyle = 'default';
    public $headerStyle = 'default';
    public $borderRadius = 'medium';
    public $fontSize = 'medium';
    public $compactMode = false;
    public $animationsEnabled = true;
    public $customCss = '';

    public $availableThemes = [
        'light' => 'Light Theme',
        'dark' => 'Dark Theme',
        'auto' => 'Auto (System)',
    ];

    public $availableColors = [
        '#3b82f6' => 'Blue',
        '#10b981' => 'Green',
        '#f59e0b' => 'Yellow',
        '#ef4444' => 'Red',
        '#8b5cf6' => 'Purple',
        '#06b6d4' => 'Cyan',
        '#f97316' => 'Orange',
        '#ec4899' => 'Pink',
    ];

    public $sidebarStyles = [
        'default' => 'Default',
        'compact' => 'Compact',
        'minimal' => 'Minimal',
    ];

    public $headerStyles = [
        'default' => 'Default',
        'transparent' => 'Transparent',
        'solid' => 'Solid',
    ];

    public function mount()
    {
        // Load existing theme settings
        $user = auth()->user();
        if ($user) {
            $this->theme = $user->theme ?? 'light';
            $this->primaryColor = $user->primary_color ?? '#3b82f6';
            $this->accentColor = $user->accent_color ?? '#10b981';
            $this->sidebarStyle = $user->sidebar_style ?? 'default';
            $this->headerStyle = $user->header_style ?? 'default';
            $this->borderRadius = $user->border_radius ?? 'medium';
            $this->fontSize = $user->font_size ?? 'medium';
            $this->compactMode = $user->compact_mode ?? false;
            $this->animationsEnabled = $user->animations_enabled ?? true;
            $this->customCss = $user->custom_css ?? '';
        }
    }

    public function save()
    {
        $this->validate([
            'theme' => 'required|in:light,dark,auto',
            'primaryColor' => 'required|string',
            'accentColor' => 'required|string',
            'sidebarStyle' => 'required|in:default,compact,minimal',
            'headerStyle' => 'required|in:default,transparent,solid',
            'borderRadius' => 'required|in:none,small,medium,large',
            'fontSize' => 'required|in:small,medium,large',
            'customCss' => 'nullable|string|max:5000',
        ]);

        // Save theme settings
        $user = auth()->user();
        if ($user) {
            $user->update([
                'theme' => $this->theme,
                'primary_color' => $this->primaryColor,
                'accent_color' => $this->accentColor,
                'sidebar_style' => $this->sidebarStyle,
                'header_style' => $this->headerStyle,
                'border_radius' => $this->borderRadius,
                'font_size' => $this->fontSize,
                'compact_mode' => $this->compactMode,
                'animations_enabled' => $this->animationsEnabled,
                'custom_css' => $this->customCss,
            ]);
        }

        session()->flash('message', 'Theme settings updated successfully!');
        
        // Emit event to update theme in real-time
        $this->dispatch('theme-updated', [
            'theme' => $this->theme,
            'primaryColor' => $this->primaryColor,
            'accentColor' => $this->accentColor,
        ]);
    }

    public function cancel()
    {
        $this->mount(); // Reset to original values
    }

    public function resetToDefaults()
    {
        $this->theme = 'light';
        $this->primaryColor = '#3b82f6';
        $this->accentColor = '#10b981';
        $this->sidebarStyle = 'default';
        $this->headerStyle = 'default';
        $this->borderRadius = 'medium';
        $this->fontSize = 'medium';
        $this->compactMode = false;
        $this->animationsEnabled = true;
        $this->customCss = '';
        
        session()->flash('message', 'Theme settings reset to defaults!');
    }

    public function previewTheme($theme)
    {
        $this->theme = $theme;
        $this->dispatch('theme-preview', ['theme' => $theme]);
    }

    public function render()
    {
        return view('livewire.settings.theming-tab');
    }
}
