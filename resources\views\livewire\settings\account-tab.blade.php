<div class="card">
    <div class="flex flex-col items-center space-y-4 border-b border-slate-200 p-4 dark:border-navy-500 sm:flex-row sm:justify-between sm:space-y-0 sm:px-5">
        <h2 class="text-lg font-medium tracking-wide text-slate-700 dark:text-navy-100">
            Account Settings
        </h2>
        <div class="flex justify-center space-x-2">
            <button wire:click="cancel"
                class="btn min-w-[7rem] rounded-full border border-slate-300 font-medium text-slate-700 hover:bg-slate-150 focus:bg-slate-150 active:bg-slate-150/80 dark:border-navy-450 dark:text-navy-100 dark:hover:bg-navy-500 dark:focus:bg-navy-500 dark:active:bg-navy-500/90">
                Cancel
            </button>
            <button wire:click="save"
                class="btn min-w-[7rem] rounded-full bg-primary font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90 dark:bg-accent dark:hover:bg-accent-focus dark:focus:bg-accent-focus dark:active:bg-accent/90">
                Save
            </button>
        </div>
    </div>

    <div class="p-4 sm:p-5">
        <!-- Flash Messages -->
        @if (session()->has('message'))
            <div class="alert mb-4 flex rounded-lg border border-success bg-success/10 py-4 px-4 text-success sm:px-5">
                <svg xmlns="http://www.w3.org/2000/svg" class="size-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                <div class="ml-2">
                    <h3 class="text-sm font-medium">{{ session('message') }}</h3>
                </div>
            </div>
        @endif

        <!-- Avatar Section -->
        <div class="flex flex-col">
            <span class="text-base font-medium text-slate-600 dark:text-navy-100">Avatar</span>
            <div class="avatar mt-1.5 size-20">
                @if($avatar)
                    <img class="mask is-squircle" src="{{ $avatar->temporaryUrl() }}" alt="avatar" />
                @else
                    <img class="mask is-squircle" src="{{ asset('images/200x200.png') }}" alt="avatar" />
                @endif
                <div class="absolute bottom-0 right-0 flex items-center justify-center rounded-full bg-white dark:bg-navy-700">
                    <label for="avatar-upload"
                        class="btn size-6 rounded-full border border-slate-200 p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:border-navy-500 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25 cursor-pointer">
                        <svg xmlns="http://www.w3.org/2000/svg" class="size-3.5" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                        </svg>
                    </label>
                    <input type="file" id="avatar-upload" wire:model="avatar" accept="image/*" class="hidden" />
                </div>
            </div>
            @error('avatar') <span class="text-xs text-error">{{ $message }}</span> @enderror
        </div>

        <div class="my-7 h-px bg-slate-200 dark:bg-navy-500"></div>

        <!-- Form Fields -->
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <label class="block">
                <span>Display Name <span class="text-error">*</span></span>
                <span class="relative mt-1.5 flex">
                    <input wire:model="displayName"
                        class="form-input peer w-full rounded-full border border-slate-300 bg-transparent px-3 py-2 pl-9 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                        placeholder="Enter display name" type="text" />
                    <span class="pointer-events-none absolute flex h-full w-10 items-center justify-center text-slate-400 peer-focus:text-primary dark:text-navy-300 dark:peer-focus:text-accent">
                        <i class="fa-regular fa-user text-base"></i>
                    </span>
                </span>
                @error('displayName') <span class="text-xs text-error">{{ $message }}</span> @enderror
            </label>

            <label class="block">
                <span>Full Name <span class="text-error">*</span></span>
                <span class="relative mt-1.5 flex">
                    <input wire:model="fullName"
                        class="form-input peer w-full rounded-full border border-slate-300 bg-transparent px-3 py-2 pl-9 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                        placeholder="Enter full name" type="text" />
                    <span class="pointer-events-none absolute flex h-full w-10 items-center justify-center text-slate-400 peer-focus:text-primary dark:text-navy-300 dark:peer-focus:text-accent">
                        <i class="fa-regular fa-user text-base"></i>
                    </span>
                </span>
                @error('fullName') <span class="text-xs text-error">{{ $message }}</span> @enderror
            </label>

            <label class="block">
                <span>Email Address <span class="text-error">*</span></span>
                <span class="relative mt-1.5 flex">
                    <input wire:model="email"
                        class="form-input peer w-full rounded-full border border-slate-300 bg-transparent px-3 py-2 pl-9 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                        placeholder="Enter email address" type="email" />
                    <span class="pointer-events-none absolute flex h-full w-10 items-center justify-center text-slate-400 peer-focus:text-primary dark:text-navy-300 dark:peer-focus:text-accent">
                        <i class="fa-regular fa-envelope text-base"></i>
                    </span>
                </span>
                @error('email') <span class="text-xs text-error">{{ $message }}</span> @enderror
            </label>

            <label class="block">
                <span>Phone Number</span>
                <span class="relative mt-1.5 flex">
                    <input wire:model="phoneNumber"
                        class="form-input peer w-full rounded-full border border-slate-300 bg-transparent px-3 py-2 pl-9 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                        placeholder="Enter phone number" type="text" />
                    <span class="pointer-events-none absolute flex h-full w-10 items-center justify-center text-slate-400 peer-focus:text-primary dark:text-navy-300 dark:peer-focus:text-accent">
                        <i class="fa fa-phone"></i>
                    </span>
                </span>
                @error('phoneNumber') <span class="text-xs text-error">{{ $message }}</span> @enderror
            </label>
        </div>

        <div class="my-7 h-px bg-slate-200 dark:bg-navy-500"></div>

        <!-- Linked Accounts Section -->
        <div>
            <h3 class="text-base font-medium text-slate-600 dark:text-navy-100">
                Linked Accounts
            </h3>
            <p class="text-xs-plus text-slate-400 dark:text-navy-300">
                Connect your account with third-party services for easier sign-in and enhanced features.
            </p>
            <div class="flex items-center justify-between pt-4">
                <div class="flex items-center space-x-4">
                    <div class="size-12">
                        <img src="{{ asset('images/logos/google.svg') }}" alt="Google logo" />
                    </div>
                    <p class="font-medium line-clamp-1">
                        Sign In with Google
                    </p>
                </div>
                <button wire:click="connectGoogle"
                    class="btn h-8 rounded-full border border-slate-200 px-3 text-xs-plus font-medium text-primary hover:bg-slate-150 focus:bg-slate-150 active:bg-slate-150/80 dark:border-navy-500 dark:text-accent-light dark:hover:bg-navy-500 dark:focus:bg-navy-500 dark:active:bg-navy-500/90">
                    Connect
                </button>
            </div>
        </div>
    </div>
</div>
