<div class="card">
    <div class="flex flex-col items-center space-y-4 border-b border-slate-200 p-4 dark:border-navy-500 sm:flex-row sm:justify-between sm:space-y-0 sm:px-5">
        <h2 class="text-lg font-medium tracking-wide text-slate-700 dark:text-navy-100">
            Theme & Appearance
        </h2>
        <div class="flex justify-center space-x-2">
            <button wire:click="resetToDefaults"
                class="btn min-w-[7rem] rounded-full border border-slate-300 font-medium text-slate-700 hover:bg-slate-150 focus:bg-slate-150 active:bg-slate-150/80 dark:border-navy-450 dark:text-navy-100 dark:hover:bg-navy-500 dark:focus:bg-navy-500 dark:active:bg-navy-500/90">
                Reset
            </button>
            <button wire:click="cancel"
                class="btn min-w-[7rem] rounded-full border border-slate-300 font-medium text-slate-700 hover:bg-slate-150 focus:bg-slate-150 active:bg-slate-150/80 dark:border-navy-450 dark:text-navy-100 dark:hover:bg-navy-500 dark:focus:bg-navy-500 dark:active:bg-navy-500/90">
                Cancel
            </button>
            <button wire:click="save"
                class="btn min-w-[7rem] rounded-full bg-primary font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90 dark:bg-accent dark:hover:bg-accent-focus dark:focus:bg-accent-focus dark:active:bg-accent/90">
                Save
            </button>
        </div>
    </div>

    <div class="p-4 sm:p-5">
        <!-- Flash Messages -->
        <!--[if BLOCK]><![endif]--><?php if(session()->has('message')): ?>
            <div class="alert mb-4 flex rounded-lg border border-success bg-success/10 py-4 px-4 text-success sm:px-5">
                <svg xmlns="http://www.w3.org/2000/svg" class="size-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                <div class="ml-2">
                    <h3 class="text-sm font-medium"><?php echo e(session('message')); ?></h3>
                </div>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        <div class="space-y-6">
            <!-- Theme Selection -->
            <div>
                <h3 class="text-base font-medium text-slate-600 dark:text-navy-100 mb-4">
                    Theme Mode
                </h3>
                
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $availableThemes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $themeKey => $themeName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <label class="flex cursor-pointer items-center space-x-3 rounded-lg border p-4 <?php echo e($theme === $themeKey ? 'border-primary bg-primary/5 dark:border-accent dark:bg-accent/5' : 'border-slate-200 dark:border-navy-500'); ?>">
                            <input wire:model="theme" value="<?php echo e($themeKey); ?>" type="radio" class="form-radio text-primary dark:text-accent" />
                            <div>
                                <p class="font-medium"><?php echo e($themeName); ?></p>
                                <p class="text-xs text-slate-400 dark:text-navy-300">
                                    <!--[if BLOCK]><![endif]--><?php if($themeKey === 'light'): ?> Light theme for daytime use
                                    <?php elseif($themeKey === 'dark'): ?> Dark theme for nighttime use
                                    <?php else: ?> Automatically switch based on system preference
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </p>
                            </div>
                        </label>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>

            <div class="my-7 h-px bg-slate-200 dark:bg-navy-500"></div>

            <!-- Color Scheme -->
            <div>
                <h3 class="text-base font-medium text-slate-600 dark:text-navy-100 mb-4">
                    Color Scheme
                </h3>
                
                <div class="grid grid-cols-2 gap-4">
                    <!-- Primary Color -->
                    <div>
                        <label class="block mb-2">
                            <span>Primary Color</span>
                        </label>
                        <div class="grid grid-cols-4 gap-2">
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $availableColors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $colorValue => $colorName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <button wire:click="$set('primaryColor', '<?php echo e($colorValue); ?>')"
                                    class="size-10 rounded-lg border-2 <?php echo e($primaryColor === $colorValue ? 'border-slate-800 dark:border-white' : 'border-slate-200 dark:border-navy-500'); ?>"
                                    style="background-color: <?php echo e($colorValue); ?>"
                                    title="<?php echo e($colorName); ?>">
                                </button>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                        <input wire:model="primaryColor" type="color" class="mt-2 form-input w-full h-10 rounded-lg" />
                    </div>

                    <!-- Accent Color -->
                    <div>
                        <label class="block mb-2">
                            <span>Accent Color</span>
                        </label>
                        <div class="grid grid-cols-4 gap-2">
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $availableColors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $colorValue => $colorName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <button wire:click="$set('accentColor', '<?php echo e($colorValue); ?>')"
                                    class="size-10 rounded-lg border-2 <?php echo e($accentColor === $colorValue ? 'border-slate-800 dark:border-white' : 'border-slate-200 dark:border-navy-500'); ?>"
                                    style="background-color: <?php echo e($colorValue); ?>"
                                    title="<?php echo e($colorName); ?>">
                                </button>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                        <input wire:model="accentColor" type="color" class="mt-2 form-input w-full h-10 rounded-lg" />
                    </div>
                </div>
            </div>

            <div class="my-7 h-px bg-slate-200 dark:bg-navy-500"></div>

            <!-- Layout Options -->
            <div>
                <h3 class="text-base font-medium text-slate-600 dark:text-navy-100 mb-4">
                    Layout Options
                </h3>
                
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <!-- Sidebar Style -->
                    <label class="block">
                        <span>Sidebar Style</span>
                        <select wire:model="sidebarStyle"
                            class="form-select mt-1.5 w-full rounded-lg border border-slate-300 bg-white px-3 py-2 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:bg-navy-700 dark:hover:border-navy-400 dark:focus:border-accent">
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $sidebarStyles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $styleKey => $styleName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($styleKey); ?>"><?php echo e($styleName); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </select>
                    </label>

                    <!-- Header Style -->
                    <label class="block">
                        <span>Header Style</span>
                        <select wire:model="headerStyle"
                            class="form-select mt-1.5 w-full rounded-lg border border-slate-300 bg-white px-3 py-2 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:bg-navy-700 dark:hover:border-navy-400 dark:focus:border-accent">
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $headerStyles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $styleKey => $styleName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($styleKey); ?>"><?php echo e($styleName); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </select>
                    </label>

                    <!-- Border Radius -->
                    <label class="block">
                        <span>Border Radius</span>
                        <select wire:model="borderRadius"
                            class="form-select mt-1.5 w-full rounded-lg border border-slate-300 bg-white px-3 py-2 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:bg-navy-700 dark:hover:border-navy-400 dark:focus:border-accent">
                            <option value="none">None</option>
                            <option value="small">Small</option>
                            <option value="medium">Medium</option>
                            <option value="large">Large</option>
                        </select>
                    </label>

                    <!-- Font Size -->
                    <label class="block">
                        <span>Font Size</span>
                        <select wire:model="fontSize"
                            class="form-select mt-1.5 w-full rounded-lg border border-slate-300 bg-white px-3 py-2 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:bg-navy-700 dark:hover:border-navy-400 dark:focus:border-accent">
                            <option value="small">Small</option>
                            <option value="medium">Medium</option>
                            <option value="large">Large</option>
                        </select>
                    </label>
                </div>
            </div>

            <div class="my-7 h-px bg-slate-200 dark:bg-navy-500"></div>

            <!-- Advanced Options -->
            <div>
                <h3 class="text-base font-medium text-slate-600 dark:text-navy-100 mb-4">
                    Advanced Options
                </h3>
                
                <div class="space-y-4">
                    <label class="inline-flex items-center space-x-2">
                        <input wire:model="compactMode"
                            class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                            type="checkbox" />
                        <span>Compact Mode</span>
                    </label>

                    <label class="inline-flex items-center space-x-2">
                        <input wire:model="animationsEnabled"
                            class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                            type="checkbox" />
                        <span>Enable Animations</span>
                    </label>
                </div>

                <!-- Custom CSS -->
                <div class="mt-6">
                    <label class="block">
                        <span>Custom CSS</span>
                        <textarea wire:model="customCss"
                            class="form-textarea mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                            rows="6"
                            placeholder="/* Add your custom CSS here */"></textarea>
                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['customCss'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-xs text-error"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        <p class="text-xs text-slate-400 dark:text-navy-300 mt-1">
                            Add custom CSS to override default styles. Use with caution.
                        </p>
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH D:\Laravel_Projects\alhars\resources\views/livewire/settings/theming-tab.blade.php ENDPATH**/ ?>