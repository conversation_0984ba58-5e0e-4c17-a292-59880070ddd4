<div class="space-y-6">
    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="alert flex rounded-lg border border-success bg-success/10 py-4 px-4 text-success sm:px-5">
            <svg xmlns="http://www.w3.org/2000/svg" class="size-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            <div class="ml-2">
                <h3 class="text-sm font-medium">{{ session('message') }}</h3>
            </div>
        </div>
    @endif

    <!-- Connection Status -->
    <div class="flex items-center justify-between p-4 border border-slate-200 rounded-lg dark:border-navy-500 {{ $isConnected ? 'bg-success/5 border-success/20' : 'bg-slate-50 dark:bg-navy-800' }}">
        <div class="flex items-center space-x-3">
            <div class="avatar size-10">
                <div class="is-initial rounded-full bg-indigo-600 text-white">
                    <i class="fab fa-discord text-lg"></i>
                </div>
            </div>
            <div>
                <h4 class="font-medium">Discord Bot Connection</h4>
                <p class="text-sm {{ $isConnected ? 'text-success' : 'text-slate-400 dark:text-navy-300' }}">
                    {{ $isConnected ? 'Bot Connected and Active' : 'Bot Not Connected' }}
                </p>
            </div>
        </div>
        @if($isConnected)
            <div class="badge bg-success/10 text-success dark:bg-success/15">
                <div class="size-2 rounded-full bg-success mr-1"></div>
                Connected
            </div>
        @endif
    </div>

    <!-- Bot Configuration -->
    <div class="space-y-4">
        <h4 class="font-medium text-slate-700 dark:text-navy-100">Bot Configuration</h4>
        
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <label class="block">
                <span>Bot Token <span class="text-error">*</span></span>
                <input wire:model="botToken"
                    class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                    placeholder="Your Discord Bot Token" type="password" />
                @error('botToken') <span class="text-xs text-error">{{ $message }}</span> @enderror
            </label>

            <label class="block">
                <span>Client ID <span class="text-error">*</span></span>
                <input wire:model="clientId"
                    class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                    placeholder="Discord Application Client ID" type="text" />
                @error('clientId') <span class="text-xs text-error">{{ $message }}</span> @enderror
            </label>

            <label class="block">
                <span>Client Secret <span class="text-error">*</span></span>
                <input wire:model="clientSecret"
                    class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                    placeholder="Discord Application Client Secret" type="password" />
                @error('clientSecret') <span class="text-xs text-error">{{ $message }}</span> @enderror
            </label>

            <label class="block">
                <span>Guild ID</span>
                <input wire:model="guildId"
                    class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                    placeholder="Discord Server ID" type="text" />
                @error('guildId') <span class="text-xs text-error">{{ $message }}</span> @enderror
            </label>
        </div>
    </div>

    <!-- Channel Configuration -->
    <div class="space-y-4">
        <h4 class="font-medium text-slate-700 dark:text-navy-100">Channel Configuration</h4>
        
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <label class="block">
                <span>Welcome Channel ID</span>
                <input wire:model="welcomeChannelId"
                    class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                    placeholder="Channel ID for welcome messages" type="text" />
                @error('welcomeChannelId') <span class="text-xs text-error">{{ $message }}</span> @enderror
            </label>

            <label class="block">
                <span>Log Channel ID</span>
                <input wire:model="logChannelId"
                    class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                    placeholder="Channel ID for bot logs" type="text" />
                @error('logChannelId') <span class="text-xs text-error">{{ $message }}</span> @enderror
            </label>
        </div>

        <label class="block">
            <span>Welcome Message <span class="text-error">*</span></span>
            <textarea wire:model="welcomeMessage"
                class="form-textarea mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                rows="3" placeholder="Welcome message for new members (use {user} for mention)"></textarea>
            @error('welcomeMessage') <span class="text-xs text-error">{{ $message }}</span> @enderror
        </label>

        <label class="block">
            <span>Auto Role ID</span>
            <input wire:model="autoRoleId"
                class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                placeholder="Role ID to assign to new members" type="text" />
            @error('autoRoleId') <span class="text-xs text-error">{{ $message }}</span> @enderror
        </label>
    </div>

    <!-- Bot Features -->
    <div class="space-y-4">
        <h4 class="font-medium text-slate-700 dark:text-navy-100">Bot Features</h4>
        
        <div class="space-y-2">
            <label class="inline-flex items-center space-x-2">
                <input wire:model="enableSlashCommands"
                    class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                    type="checkbox" />
                <span>Enable Slash Commands</span>
            </label>

            <label class="inline-flex items-center space-x-2">
                <input wire:model="enableMessageCommands"
                    class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                    type="checkbox" />
                <span>Enable Message Commands</span>
            </label>

            <label class="inline-flex items-center space-x-2">
                <input wire:model="enableAutoModeration"
                    class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                    type="checkbox" />
                <span>Enable Auto Moderation</span>
            </label>

            <label class="inline-flex items-center space-x-2">
                <input wire:model="enableMusicBot"
                    class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                    type="checkbox" />
                <span>Enable Music Bot</span>
            </label>

            <label class="inline-flex items-center space-x-2">
                <input wire:model="enableEconomyBot"
                    class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                    type="checkbox" />
                <span>Enable Economy Bot</span>
            </label>

            <label class="inline-flex items-center space-x-2">
                <input wire:model="enableLevelingSystem"
                    class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                    type="checkbox" />
                <span>Enable Leveling System</span>
            </label>

            <label class="inline-flex items-center space-x-2">
                <input wire:model="enableDMCommands"
                    class="form-switch h-5 w-10 rounded-lg bg-slate-300 before:rounded-md before:bg-slate-50 checked:bg-slate-500 checked:before:bg-white dark:bg-navy-900 dark:before:bg-navy-300 dark:checked:bg-navy-400 dark:checked:before:bg-white"
                    type="checkbox" />
                <span>Enable DM Commands</span>
            </label>
        </div>
    </div>

    <!-- Rate Limiting -->
    <div class="space-y-4">
        <h4 class="font-medium text-slate-700 dark:text-navy-100">Rate Limiting</h4>
        
        <label class="block">
            <span>Max Messages/Minute <span class="text-error">*</span></span>
            <input wire:model="maxMessagesPerMinute"
                class="form-input mt-1.5 w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                type="number" min="1" max="200" />
            @error('maxMessagesPerMinute') <span class="text-xs text-error">{{ $message }}</span> @enderror
        </label>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-between pt-4 border-t border-slate-200 dark:border-navy-500">
        <div class="flex space-x-2">
            @if($isConnected)
                <button wire:click="testConnection"
                    class="btn border border-slate-300 text-slate-700 hover:bg-slate-150 focus:bg-slate-150 active:bg-slate-150/80 dark:border-navy-450 dark:text-navy-100 dark:hover:bg-navy-500 dark:focus:bg-navy-500 dark:active:bg-navy-500/90">
                    Test Connection
                </button>
                <button wire:click="disconnect"
                    class="btn border border-error text-error hover:bg-error/10 focus:bg-error/10 active:bg-error/20">
                    Disconnect
                </button>
            @endif
        </div>
        
        <div class="flex space-x-2">
            <button wire:click="save"
                class="btn bg-primary font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90 dark:bg-accent dark:hover:bg-accent-focus dark:focus:bg-accent-focus dark:active:bg-accent/90">
                {{ $isConnected ? 'Update Settings' : 'Connect Bot' }}
            </button>
        </div>
    </div>
</div>
